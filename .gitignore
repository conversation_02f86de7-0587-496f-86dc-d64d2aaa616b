# Logs
logs
*.log*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.yarn-cache

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Yarn
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Windows
Thumbs.db

# Project
node_modules
dist
out
mcp_server
stats.html

# ENV
.env
.env.*
!.env.example

# Local
local
.aider*
.cursorrules
.cursor/*
.claude/*
.gemini/*
.trae/*
.claude-code-router/*

# vitest
coverage
.vitest-cache
vitest.config.*.timestamp-*

# playwright
playwright-report
test-results

YOUR_MEMORY_FILE_PATH

src/renderer/src/assets/live2d/models
my_experiment/pixi-live2d-display_docs