diff --git a/client.js b/client.js
index 33b4ff6309d5f29187dab4e285d07dac20340bab..8f568637ee9e4677585931fb0284c8165a933f69 100644
--- a/client.js
+++ b/client.js
@@ -433,7 +433,7 @@ class OpenAI {
                 'User-Agent': this.getUserAgent(),
                 'X-Stainless-Retry-Count': String(retryCount),
                 ...(options.timeout ? { 'X-Stainless-Timeout': String(Math.trunc(options.timeout / 1000)) } : {}),
-                ...(0, detect_platform_1.getPlatformHeaders)(),
+                // ...(0, detect_platform_1.getPlatformHeaders)(),
                 'OpenAI-Organization': this.organization,
                 'OpenAI-Project': this.project,
             },
diff --git a/client.mjs b/client.mjs
index c34c18213073540ebb296ea540b1d1ad39527906..1ce1a98256d7e90e26ca963582f235b23e996e73 100644
--- a/client.mjs
+++ b/client.mjs
@@ -430,7 +430,7 @@ export class OpenAI {
                 'User-Agent': this.getUserAgent(),
                 'X-Stainless-Retry-Count': String(retryCount),
                 ...(options.timeout ? { 'X-Stainless-Timeout': String(Math.trunc(options.timeout / 1000)) } : {}),
-                ...getPlatformHeaders(),
+                // ...getPlatformHeaders(),
                 'OpenAI-Organization': this.organization,
                 'OpenAI-Project': this.project,
             },
diff --git a/core/error.js b/core/error.js
index a12d9d9ccd242050161adeb0f82e1b98d9e78e20..fe3a5462480558bc426deea147f864f12b36f9bd 100644
--- a/core/error.js
+++ b/core/error.js
@@ -40,7 +40,7 @@ class APIError extends OpenAIError {
         if (!status || !headers) {
             return new APIConnectionError({ message, cause: (0, errors_1.castToError)(errorResponse) });
         }
-        const error = errorResponse?.['error'];
+        const error = errorResponse?.['error'] || errorResponse;
         if (status === 400) {
             return new BadRequestError(status, error, message, headers);
         }
diff --git a/core/error.mjs b/core/error.mjs
index 83cefbaffeb8c657536347322d8de9516af479a2..63334b7972ec04882aa4a0800c1ead5982345045 100644
--- a/core/error.mjs
+++ b/core/error.mjs
@@ -36,7 +36,7 @@ export class APIError extends OpenAIError {
         if (!status || !headers) {
             return new APIConnectionError({ message, cause: castToError(errorResponse) });
         }
-        const error = errorResponse?.['error'];
+        const error = errorResponse?.['error'] || errorResponse;
         if (status === 400) {
             return new BadRequestError(status, error, message, headers);
         }
diff --git a/resources/embeddings.js b/resources/embeddings.js
index 2404264d4ba0204322548945ebb7eab3bea82173..8f1bc45cc45e0797d50989d96b51147b90ae6790 100644
--- a/resources/embeddings.js
+++ b/resources/embeddings.js
@@ -5,52 +5,64 @@ exports.Embeddings = void 0;
 const resource_1 = require("../core/resource.js");
 const utils_1 = require("../internal/utils.js");
 class Embeddings extends resource_1.APIResource {
-    /**
-     * Creates an embedding vector representing the input text.
-     *
-     * @example
-     * ```ts
-     * const createEmbeddingResponse =
-     *   await client.embeddings.create({
-     *     input: 'The quick brown fox jumped over the lazy dog',
-     *     model: 'text-embedding-3-small',
-     *   });
-     * ```
-     */
-    create(body, options) {
-        const hasUserProvidedEncodingFormat = !!body.encoding_format;
-        // No encoding_format specified, defaulting to base64 for performance reasons
-        // See https://github.com/openai/openai-node/pull/1312
-        let encoding_format = hasUserProvidedEncodingFormat ? body.encoding_format : 'base64';
-        if (hasUserProvidedEncodingFormat) {
-            (0, utils_1.loggerFor)(this._client).debug('embeddings/user defined encoding_format:', body.encoding_format);
-        }
-        const response = this._client.post('/embeddings', {
-            body: {
-                ...body,
-                encoding_format: encoding_format,
-            },
-            ...options,
-        });
-        // if the user specified an encoding_format, return the response as-is
-        if (hasUserProvidedEncodingFormat) {
-            return response;
-        }
-        // in this stage, we are sure the user did not specify an encoding_format
-        // and we defaulted to base64 for performance reasons
-        // we are sure then that the response is base64 encoded, let's decode it
-        // the returned result will be a float32 array since this is OpenAI API's default encoding
-        (0, utils_1.loggerFor)(this._client).debug('embeddings/decoding base64 embeddings from base64');
-        return response._thenUnwrap((response) => {
-            if (response && response.data) {
-                response.data.forEach((embeddingBase64Obj) => {
-                    const embeddingBase64Str = embeddingBase64Obj.embedding;
-                    embeddingBase64Obj.embedding = (0, utils_1.toFloat32Array)(embeddingBase64Str);
-                });
-            }
-            return response;
-        });
-    }
+	/**
+	 * Creates an embedding vector representing the input text.
+	 *
+	 * @example
+	 * ```ts
+	 * const createEmbeddingResponse =
+	 *   await client.embeddings.create({
+	 *     input: 'The quick brown fox jumped over the lazy dog',
+	 *     model: 'text-embedding-3-small',
+	 *   });
+	 * ```
+	 */
+	create(body, options) {
+		const hasUserProvidedEncodingFormat = !!body.encoding_format;
+		// No encoding_format specified, defaulting to base64 for performance reasons
+		// See https://github.com/openai/openai-node/pull/1312
+		let encoding_format = hasUserProvidedEncodingFormat
+			? body.encoding_format
+			: "base64";
+		if (body.model.includes("jina")) {
+			encoding_format = undefined;
+		}
+		if (hasUserProvidedEncodingFormat) {
+			(0, utils_1.loggerFor)(this._client).debug(
+				"embeddings/user defined encoding_format:",
+				body.encoding_format
+			);
+		}
+		const response = this._client.post("/embeddings", {
+			body: {
+				...body,
+				encoding_format: encoding_format,
+			},
+			...options,
+		});
+		// if the user specified an encoding_format, return the response as-is
+		if (hasUserProvidedEncodingFormat || body.model.includes("jina")) {
+			return response;
+		}
+		// in this stage, we are sure the user did not specify an encoding_format
+		// and we defaulted to base64 for performance reasons
+		// we are sure then that the response is base64 encoded, let's decode it
+		// the returned result will be a float32 array since this is OpenAI API's default encoding
+		(0, utils_1.loggerFor)(this._client).debug(
+			"embeddings/decoding base64 embeddings from base64"
+		);
+		return response._thenUnwrap((response) => {
+			if (response && response.data && typeof response.data[0]?.embedding === 'string') {
+				response.data.forEach((embeddingBase64Obj) => {
+					const embeddingBase64Str = embeddingBase64Obj.embedding;
+					embeddingBase64Obj.embedding = (0, utils_1.toFloat32Array)(
+						embeddingBase64Str
+					);
+				});
+			}
+			return response;
+		});
+	}
 }
 exports.Embeddings = Embeddings;
 //# sourceMappingURL=embeddings.js.map
diff --git a/resources/embeddings.mjs b/resources/embeddings.mjs
index 19dcaef578c194a89759c4360073cfd4f7dd2cbf..0284e9cc615c900eff508eb595f7360a74bd9200 100644
--- a/resources/embeddings.mjs
+++ b/resources/embeddings.mjs
@@ -2,51 +2,61 @@
 import { APIResource } from "../core/resource.mjs";
 import { loggerFor, toFloat32Array } from "../internal/utils.mjs";
 export class Embeddings extends APIResource {
-    /**
-     * Creates an embedding vector representing the input text.
-     *
-     * @example
-     * ```ts
-     * const createEmbeddingResponse =
-     *   await client.embeddings.create({
-     *     input: 'The quick brown fox jumped over the lazy dog',
-     *     model: 'text-embedding-3-small',
-     *   });
-     * ```
-     */
-    create(body, options) {
-        const hasUserProvidedEncodingFormat = !!body.encoding_format;
-        // No encoding_format specified, defaulting to base64 for performance reasons
-        // See https://github.com/openai/openai-node/pull/1312
-        let encoding_format = hasUserProvidedEncodingFormat ? body.encoding_format : 'base64';
-        if (hasUserProvidedEncodingFormat) {
-            loggerFor(this._client).debug('embeddings/user defined encoding_format:', body.encoding_format);
-        }
-        const response = this._client.post('/embeddings', {
-            body: {
-                ...body,
-                encoding_format: encoding_format,
-            },
-            ...options,
-        });
-        // if the user specified an encoding_format, return the response as-is
-        if (hasUserProvidedEncodingFormat) {
-            return response;
-        }
-        // in this stage, we are sure the user did not specify an encoding_format
-        // and we defaulted to base64 for performance reasons
-        // we are sure then that the response is base64 encoded, let's decode it
-        // the returned result will be a float32 array since this is OpenAI API's default encoding
-        loggerFor(this._client).debug('embeddings/decoding base64 embeddings from base64');
-        return response._thenUnwrap((response) => {
-            if (response && response.data) {
-                response.data.forEach((embeddingBase64Obj) => {
-                    const embeddingBase64Str = embeddingBase64Obj.embedding;
-                    embeddingBase64Obj.embedding = toFloat32Array(embeddingBase64Str);
-                });
-            }
-            return response;
-        });
-    }
+	/**
+	 * Creates an embedding vector representing the input text.
+	 *
+	 * @example
+	 * ```ts
+	 * const createEmbeddingResponse =
+	 *   await client.embeddings.create({
+	 *     input: 'The quick brown fox jumped over the lazy dog',
+	 *     model: 'text-embedding-3-small',
+	 *   });
+	 * ```
+	 */
+	create(body, options) {
+		const hasUserProvidedEncodingFormat = !!body.encoding_format;
+		// No encoding_format specified, defaulting to base64 for performance reasons
+		// See https://github.com/openai/openai-node/pull/1312
+		let encoding_format = hasUserProvidedEncodingFormat
+			? body.encoding_format
+			: "base64";
+		if (body.model.includes("jina")) {
+			encoding_format = undefined;
+		}
+		if (hasUserProvidedEncodingFormat) {
+			loggerFor(this._client).debug(
+				"embeddings/user defined encoding_format:",
+				body.encoding_format
+			);
+		}
+		const response = this._client.post("/embeddings", {
+			body: {
+				...body,
+				encoding_format: encoding_format,
+			},
+			...options,
+		});
+		// if the user specified an encoding_format, return the response as-is
+		if (hasUserProvidedEncodingFormat || body.model.includes("jina")) {
+			return response;
+		}
+		// in this stage, we are sure the user did not specify an encoding_format
+		// and we defaulted to base64 for performance reasons
+		// we are sure then that the response is base64 encoded, let's decode it
+		// the returned result will be a float32 array since this is OpenAI API's default encoding
+		loggerFor(this._client).debug(
+			"embeddings/decoding base64 embeddings from base64"
+		);
+		return response._thenUnwrap((response) => {
+			if (response && response.data && typeof response.data[0]?.embedding === 'string') {
+				response.data.forEach((embeddingBase64Obj) => {
+					const embeddingBase64Str = embeddingBase64Obj.embedding;
+					embeddingBase64Obj.embedding = toFloat32Array(embeddingBase64Str);
+				});
+			}
+			return response;
+		});
+	}
 }
 //# sourceMappingURL=embeddings.mjs.map
