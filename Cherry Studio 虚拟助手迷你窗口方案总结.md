# Cherry Studio 虚拟角色迷你窗口方案总结

## 项目概述

本项目在Cherry Studio的迷你窗口(快捷助手)中集成了Live2D虚拟角色显示的功能。

## 使用的技术

### 前端技术栈

- **React + TypeScript**: 组件开发和类型安全
- **Styled Components**: CSS-in-JS样式解决方案
- **Ant Design**: UI组件库（Button、图标等）
- **Electron**: 桌面应用框架，支持webview嵌入
- **EventEmitter**: 统一的事件通信系统
- **IPC Channels**: 跨进程通信机制

### Live2D技术

- **PIXI.js**: 2D渲染引擎
- **pixi-live2d-display**: Live2D模型显示库
- **Live2D Cubism Core**: Live2D核心运行时
- **PostMessage API**: 安全的跨窗口通信

### 动画和交互

- **CSS Transitions**: 容器展开/收起动画
- **JavaScript动画**: 模型位置平滑过渡
- **RequestAnimationFrame**: 60fps动画
- **缓动函数**: cubic-bezier和自定义缓动
- **智能队列**: 动画指令队列管理

## 项目结构

### 文件结构

```
src/renderer/src/
├── assets/live2d/                           # Live2D资源文件
│   ├── libs/                                # Live2D库文件
│   │   ├── live2dcubismcore.min.js          # Cubism 3/4需要加载
│   │   └── live2d.min.js                    # Cubism 2.1 需要加载
│   └── models/                              # 模型文件目录
│       └── hiyori_pro_zh/                   # Hiyori模型
│       └── ......
├── store/
│   └── virtualCharacter.ts                 # Redux状态管理
└── windows/mini/virtualCharacter/           # 虚拟角色组件
    ├── components/
    │   └── Live2DViewer.tsx                 # Live2D显示组件
    ├── hooks/
    │   └── useLive2D.ts                     # 统一的Live2D hook
    ├── live2d/
    │   └── live2d.html                      # 简化的Live2D页面
    ├── VirtualCharacter.tsx                 # 主组件（内联了控制、加载、错误组件）
    └── types.ts                             # 简化的类型定义
```

### 修改的文件

```
src/renderer/src/windows/mini/home/
└── HomeWindow.tsx                           # 主窗口布局

src/renderer/src/store/
├── index.ts                                 # Redux store（添加virtualCharacter slice）
└── virtualCharacter.ts                     # 新增：Live2D状态管理

src/renderer/src/services/
└── EventService.ts                          # 事件服务（新增AI_RESPONSE_RECEIVED事件）

src/renderer/src/utils/
└── prompt.ts                                # 提示词处理（简化Live2D模板变量）
```

## 架构设计

### 架构设计图

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   主窗口组件     │    │   虚拟角色组件    │    │  Live2D HTML    │
│  HomeWindow     │───▶│ VirtualCharacter │───▶│   live2d.html   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Redux Store    │    │   useLive2D      │    │  PostMessage    │
│ virtualCharacterSlice  │◄──▶│   统一Hook       │◄──▶│   简化通信       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   事件系统       │    │   Webview        │    │   Live2D 模型   │
│  EventEmitter   │    │   管理           │    │   Hiyori Pro    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 通信流程图

```
AI回复流 ──┐
          ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ EventEmitter    │───▶│   useLive2D      │───▶│   动画队列       │
│ AI_RESPONSE     │    │   Hook           │    │  AnimationQueue │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  sendToLive2D    │───▶│  PostMessage    │
                       │  (统一发送)       │    │  (直接通信)      │
                       └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  live2d.html    │
                                               │  (消息处理)      │
                                               └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  Live2D 模型    │
                                               │  (动画播放)      │
                                               └─────────────────┘
```

### 组件架构

- **主组件**: `VirtualCharacter.tsx` - 整合了所有功能，内联了控制、加载、错误组件
- **显示组件**: `Live2DViewer.tsx` - 专门处理webview和Live2D渲染
- **Live2D页面**: `live2d.html` - 简化的Live2D页面，直接处理模型渲染和交互

### 统一状态管理

- **Redux Slice**: `virtualCharacter.ts` - 集成到项目Redux store的状态管理
- **统一Hook**: `useLive2D.ts` - 整合所有Live2D相关的状态管理和交互逻辑
- **类型定义**: `types.ts` - 简化的TypeScript类型系统

### 通信机制

- **PostMessage**: 直接的webview通信，无中间层
- **EventEmitter**: 监听AI回复事件
- **动画队列**: 简单的数组队列管理动画指令

## 实现的功能

### 1. 侧边虚拟角色显示

- **默认显示**: 右侧200px宽度区域显示Live2D模型
- **响应式布局**: 主内容区域自适应剩余空间
- **独立加载**: 虚拟角色独立加载，不影响主界面

### 2. 展开/收起功能

- **展开按钮**: 右上角展开/收起按钮，图标动态切换
- **全屏模式**: 展开时占据整个迷你窗口界面
- **主内容隐藏**: 展开时主内容区域自动隐藏
- **平滑动画**: 300ms缓动过渡动画

### 3. Live2D模型交互

- **鼠标拖拽**: 按住左键拖拽模型到任意位置
- **滚轮缩放**: 10%-300%缩放范围，显示缩放百分比
- **视线跟随**: 鼠标移动时模型视线跟随（拖拽时禁用）
- **点击交互**: 点击模型触发随机动作和对话

### 4. 智能位置管理

- **动态居中**: 展开时模型自动移动到视觉中心
- **位置记忆**: 分别记录普通模式和展开模式下的位置
- **平滑过渡**: 使用缓动函数实现自然的位置变化

### 5. 状态反馈系统

- **操作提示**: 拖拽、缩放、模式切换时显示状态信息
- **加载状态**: 显示加载动画和错误处理
- **交互反馈**: 点击模型时显示随机对话消息

### 6. AI 回复动画联动

- **指令解析**: 自动解析AI回复中的动画指令 `[action:Happy]`、`[expression:Surprised]`
- **实时播放**: AI回复过程中实时播放对应的动画和表情
- **智能队列**: 避免动画冲突，确保播放顺序和流畅度
- **降级处理**: 动画播放失败时自动使用默认动画

### 7. 通信方式

- **事件系统**: 通过 `EventEmitter` 监听AI回复事件
- **PostMessage**: 直接的webview双向通信，无中间层

### 8. 错误处理

- **统一错误显示**: 通过内联的ErrorOverlay组件显示错误
- **PostMessage通信**: 错误信息通过PostMessage传递给父组件
- **重试机制**: 提供重试按钮，重新加载webview
- **用户友好**: 错误信息简洁明了，不影响使用体验
