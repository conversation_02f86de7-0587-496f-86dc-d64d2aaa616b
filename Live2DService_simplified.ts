import { loggerService } from '@logger'
import { EventEmitter, EVENT_NAMES } from './EventService'
import store from '@renderer/store'
import { 
  setLive2DVisible, 
  setLive2DLoading, 
  setLive2DError,
  setCurrentAnimation 
} from '@renderer/store/virtualCharacter'

// 简化的类型定义
interface Live2DModel {
  id: string
  name: string
  path: string
  animations: string[]
}

interface AnimationOptions {
  name: string
  loop?: boolean
  priority?: number
}

/**
 * Live2D 服务 - 单文件实现
 * 
 * 遵循 Cherry Studio 的服务设计风格：
 * - 单例模式
 * - 职责单一
 * - 简洁实用
 * - 与现有架构集成
 */
class Live2DService {
  private static instance: Live2DService
  private logger = loggerService.withContext('Live2DService')
  
  // 服务状态
  private currentModel: Live2DModel | null = null
  private isInitialized = false
  private animationQueue: AnimationOptions[] = []
  private isPlayingAnimation = false

  private constructor() {
    this.setupEventListeners()
  }

  public static getInstance(): Live2DService {
    if (!Live2DService.instance) {
      Live2DService.instance = new Live2DService()
    }
    return Live2DService.instance
  }

  /**
   * 初始化服务
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.logger.warn('Live2D 服务已初始化')
      return
    }

    try {
      this.logger.info('初始化 Live2D 服务')
      
      // 加载默认模型
      await this.loadDefaultModel()
      
      this.isInitialized = true
      EventEmitter.emit(EVENT_NAMES.LIVE2D_READY)
      
    } catch (error) {
      this.logger.error('Live2D 服务初始化失败', error as Error)
      this.handleError('初始化失败', error as Error)
    }
  }

  /**
   * 加载模型
   */
  public async loadModel(modelPath: string): Promise<void> {
    try {
      store.dispatch(setLive2DLoading(true))
      this.logger.info('加载 Live2D 模型', { modelPath })

      // 实际的模型加载逻辑
      const model = await this.performModelLoad(modelPath)
      
      this.currentModel = model
      store.dispatch(setLive2DError(null))
      
      this.logger.info('Live2D 模型加载成功', { modelId: model.id })

    } catch (error) {
      this.logger.error('Live2D 模型加载失败', error as Error, { modelPath })
      this.handleError('模型加载失败', error as Error)
    } finally {
      store.dispatch(setLive2DLoading(false))
    }
  }

  /**
   * 播放动画
   */
  public async playAnimation(options: AnimationOptions): Promise<void> {
    if (!this.currentModel) {
      this.logger.warn('没有加载的模型，无法播放动画')
      return
    }

    this.logger.debug('播放动画', options)
    
    // 添加到队列
    this.animationQueue.push(options)
    
    // 处理队列
    if (!this.isPlayingAnimation) {
      this.processAnimationQueue()
    }
  }

  /**
   * 设置可见性
   */
  public setVisible(visible: boolean): void {
    this.logger.info(`${visible ? '显示' : '隐藏'} Live2D 角色`)
    store.dispatch(setLive2DVisible(visible))
  }

  /**
   * 获取当前模型
   */
  public getCurrentModel(): Live2DModel | null {
    return this.currentModel
  }

  /**
   * 获取可用动画
   */
  public getAvailableAnimations(): string[] {
    return this.currentModel?.animations || []
  }

  // ==================== 私有方法 ====================

  /**
   * 加载默认模型
   */
  private async loadDefaultModel(): Promise<void> {
    const defaultPath = '/src/assets/live2d/models/hiyori_pro_zh'
    try {
      await this.loadModel(defaultPath)
    } catch (error) {
      this.logger.warn('默认模型加载失败，使用降级方案', error as Error)
      // 可以在这里实现降级逻辑
    }
  }

  /**
   * 执行模型加载
   */
  private async performModelLoad(modelPath: string): Promise<Live2DModel> {
    // 这里实现实际的 Live2D 模型加载逻辑
    // 目前返回模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: `model_${Date.now()}`,
          name: 'Hiyori',
          path: modelPath,
          animations: ['idle', 'happy', 'sad', 'surprised', 'angry']
        })
      }, 1000)
    })
  }

  /**
   * 处理动画队列
   */
  private async processAnimationQueue(): Promise<void> {
    if (this.animationQueue.length === 0 || this.isPlayingAnimation) {
      return
    }

    this.isPlayingAnimation = true

    while (this.animationQueue.length > 0) {
      const animation = this.animationQueue.shift()!
      
      try {
        await this.executeAnimation(animation)
      } catch (error) {
        this.logger.error('动画执行失败', error as Error, animation)
      }
    }

    this.isPlayingAnimation = false
  }

  /**
   * 执行单个动画
   */
  private async executeAnimation(options: AnimationOptions): Promise<void> {
    return new Promise((resolve) => {
      this.logger.debug('执行动画', options)
      
      store.dispatch(setCurrentAnimation(options.name))
      
      // 模拟动画播放
      setTimeout(() => {
        EventEmitter.emit(EVENT_NAMES.LIVE2D_ANIMATION_COMPLETE, options)
        store.dispatch(setCurrentAnimation(null))
        resolve()
      }, options.loop ? 0 : 2000)
    })
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听 AI 响应，播放开心动画
    EventEmitter.on(EVENT_NAMES.AI_RESPONSE_RECEIVED, () => {
      this.playAnimation({ name: 'happy', loop: false })
    })

    // 监听消息发送，播放思考动画
    EventEmitter.on(EVENT_NAMES.SEND_MESSAGE, () => {
      this.playAnimation({ name: 'idle', loop: true })
    })
  }

  /**
   * 错误处理
   */
  private handleError(message: string, error: Error): void {
    const errorInfo = {
      message,
      error: error.message,
      timestamp: new Date().toISOString()
    }

    store.dispatch(setLive2DError(message))
    EventEmitter.emit(EVENT_NAMES.LIVE2D_ERROR, errorInfo)
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.logger.info('清理 Live2D 服务资源')
    
    this.animationQueue = []
    this.isPlayingAnimation = false
    this.currentModel = null
    this.isInitialized = false
  }
}

// 导出服务实例 - 遵循 Cherry Studio 的导出风格
export const live2DService = Live2DService.getInstance()
export default Live2DService
