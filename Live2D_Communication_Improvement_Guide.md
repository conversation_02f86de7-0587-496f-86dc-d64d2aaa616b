# Live2D 迷你助手通信改进指南

## 改进概述

本次改进将 Live2D 迷你助手的通信方式从直接的 `executeJavaScript` 调用升级为符合项目规范的标准化通信机制。

## 主要改进内容

### 1. 新增文件

#### 通信桥接

- `src/renderer/src/windows/mini/assistant/live2d_bridge.js` - 标准化的 webview 通信桥接

#### 服务层

- `src/renderer/src/windows/mini/assistant/services/Live2DService.ts` - 改进的 Live2D 服务
- `src/renderer/src/windows/mini/assistant/services/Live2DErrorHandler.ts` - 统一的错误处理器

### 2. 修改文件

#### IPC 通道

- `packages/shared/IpcChannel.ts` - 添加 Live2D 相关的 IPC 通道

#### 事件系统

- `src/renderer/src/services/EventService.ts` - 添加 Live2D 相关事件

#### 主进程

- `src/main/ipc.ts` - 添加 Live2D IPC 处理器
- `src/preload/index.ts` - 添加 Live2D API

#### 组件和 Hooks

- `src/renderer/src/windows/mini/assistant/hooks/useLive2DInteraction.ts` - 使用新的服务
- `src/renderer/src/windows/mini/assistant/hooks/useAIResponseListener.ts` - 集成新服务
- `src/renderer/src/windows/mini/assistant/live2d_mini.html` - 使用新的通信机制

## 架构改进

### 旧架构问题

```
React Hook -> executeJavaScript -> 全局函数 -> Live2D 模型
```

- 安全风险：直接执行字符串代码
- 调试困难：难以追踪消息流
- 命名空间污染：全局函数定义

### 新架构优势

```
React Hook -> EventEmitter -> Live2DService -> PostMessage -> Live2D 模型
```

- 安全性：使用 postMessage 替代代码执行
- 可追踪：完整的消息流日志
- 标准化：符合项目通信规范

## 使用方法

### 1. 基本使用（保持兼容）

现有的使用方式保持不变：

```typescript
// 在组件中
const { sendMessage, playMotion, setExpandedMode } = useLive2DInteraction(
  webviewRef,
  isExpanded,
  onLoadStart,
  onLoadStop,
  onLoadError
)

// 发送消息
sendMessage('Hello Live2D!')

// 播放动作
playMotion('Happy')

// 设置展开模式
setExpandedMode(true)
```

### 2. 全局访问（保持兼容）

```typescript
// 通过全局对象访问
window.virtualAssistant.sendMessage('Hello!')
window.virtualAssistant.playMotion('Happy')
window.virtualAssistant.setExpression('Surprised')
window.virtualAssistant.setPose('Thinking')
window.virtualAssistant.testAnimations()
```

### 3. 事件系统（新增）

```typescript
import { EventEmitter, EVENT_NAMES } from '@renderer/services/EventService'

// 发送 Live2D 事件
EventEmitter.emit(EVENT_NAMES.LIVE2D_PLAY_ANIMATION, { motionGroup: 'Happy' })
EventEmitter.emit(EVENT_NAMES.LIVE2D_SET_EXPRESSION, { expression: 'Surprised' })
EventEmitter.emit(EVENT_NAMES.LIVE2D_SEND_MESSAGE, { message: 'Hello!' })

// 监听 Live2D 事件
EventEmitter.on(EVENT_NAMES.LIVE2D_READY, () => {
  console.log('Live2D is ready!')
})

EventEmitter.on(EVENT_NAMES.LIVE2D_ERROR, (errorData) => {
  console.error('Live2D error:', errorData)
})

EventEmitter.on(EVENT_NAMES.LIVE2D_ANIMATION_COMPLETE, (data) => {
  console.log('Animation completed:', data)
})
```

### 4. IPC API（新增）

```typescript
// 通过 IPC 调用（用于跨窗口通信）
await window.api.live2d.sendMessage('Hello!')
await window.api.live2d.playMotion('Happy')
await window.api.live2d.setExpression('Surprised')
await window.api.live2d.setPose('Thinking')
await window.api.live2d.setExpandedMode(true)

// 获取可用动画列表
const animations = await window.api.live2d.getAnimations()
console.log(animations)
// {
//   actions: ['Idle', 'Tap', 'Happy', 'Surprised', 'Thinking'],
//   poses: ['Normal', 'Thinking', 'Happy', 'Surprised'],
//   expressions: ['Default', 'Happy', 'Sad', 'Surprised', 'Angry']
// }

// 测试动画
await window.api.live2d.testAnimations()
```

## 错误处理

新的错误处理系统提供了更好的错误追踪和恢复机制：

```typescript
import { handleLive2DError } from '../services/Live2DErrorHandler'

// 处理不同类型的错误
try {
  // 一些 Live2D 操作
} catch (error) {
  handleLive2DError.animation('action', 'Happy', error)
  // 或
  handleLive2DError.webview(error, 'animation_playback')
  // 或
  handleLive2DError.communication(error, 'message_sending')
}
```

## 调试和监控

### 1. 日志系统

所有 Live2D 相关操作都会记录详细日志：

```
[Live2DService] Sending message to Live2D model { messageLength: 12 }
[Live2D Bridge] Sending API call { id: 1, method: 'receiveMessage', args: ['Hello!'] }
[Live2D Command] Received command { method: 'receiveMessage', args: ['Hello!'] }
```

### 2. 事件监控

可以监听所有 Live2D 事件来进行调试：

```typescript
// 监听所有 Live2D 相关事件
Object.values(EVENT_NAMES)
  .filter((name) => name.startsWith('LIVE2D_'))
  .forEach((eventName) => {
    EventEmitter.on(eventName, (data) => {
      console.log(`[Live2D Event] ${eventName}:`, data)
    })
  })
```

## 测试

### 1. 基本功能测试

```typescript
// 测试消息发送
window.virtualAssistant.sendMessage('测试消息')

// 测试动画播放
window.virtualAssistant.playMotion('Happy')
window.virtualAssistant.setExpression('Surprised')
window.virtualAssistant.setPose('Thinking')

// 测试动画序列
window.virtualAssistant.testAnimations()
```

### 2. 错误处理测试

```typescript
// 测试无效动画名称
window.virtualAssistant.playMotion('InvalidMotion')

// 测试空消息
window.virtualAssistant.sendMessage('')

// 查看错误统计
import { Live2DErrorHandler } from '../services/Live2DErrorHandler'
console.log(Live2DErrorHandler.getErrorStats())
```

## 迁移指南

### 对于现有代码

大部分现有代码无需修改，因为保持了向后兼容性。但建议逐步迁移到新的事件系统：

```typescript
// 旧方式（仍然支持）
window.virtualAssistant.playMotion('Happy')

// 新方式（推荐）
EventEmitter.emit(EVENT_NAMES.LIVE2D_PLAY_ANIMATION, { motionGroup: 'Happy' })
```

### 对于新功能

新功能建议直接使用事件系统或 IPC API：

```typescript
// 使用事件系统进行组件间通信
EventEmitter.emit(EVENT_NAMES.LIVE2D_SEND_MESSAGE, { message: 'Hello!' })

// 使用 IPC API 进行跨窗口通信
await window.api.live2d.sendMessage('Hello!')
```

## 性能优化

1. **消息队列**：未就绪时的消息会被队列化，避免丢失
2. **错误降级**：失败的动画会自动降级到默认动画
3. **重复检测**：避免重复执行相同的动画指令
4. **超时处理**：所有操作都有超时保护

## 安全性提升

1. **消息验证**：所有消息都经过类型检查和验证
2. **代码注入防护**：不再直接执行字符串代码
3. **错误隔离**：错误不会影响其他功能
4. **权限控制**：通过 IPC 通道进行权限管理

## 总结

本次改进大幅提升了 Live2D 迷你助手的：

- **安全性**：消除了代码注入风险
- **可维护性**：标准化的通信机制
- **可调试性**：完整的日志和事件系统
- **可扩展性**：易于添加新功能
- **稳定性**：完善的错误处理和恢复机制

同时保持了向后兼容性，确保现有功能正常运行。
