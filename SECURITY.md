# Security Policy

## 📢 Reporting a Vulnerability

At Cherry Studio, we take security seriously and appreciate your efforts to responsibly disclose vulnerabilities. If you discover a security issue, please report it as soon as possible.

**Please do not create public issues for security-related reports.**

- To report a security issue, please use the GitHub Security Advisories tab to "[Open a draft security advisory](https://github.com/CherryHQ/cherry-studio/security/advisories/new)".
- Include a detailed description of the issue, steps to reproduce, potential impact, and any possible mitigations.
- If applicable, please also attach proof-of-concept code or screenshots.

We will acknowledge your report within **72 hours** and provide a status update as we investigate.

---

## 🔒 Supported Versions

We aim to support the latest released version and one previous minor release.

| Version         | Supported        |
| --------------- | ---------------- |
| Latest (`main`) | ✅ Supported     |
| Previous minor  | ✅ Supported     |
| Older versions  | ❌ Not supported |

If you are using an unsupported version, we strongly recommend updating to the latest release to receive security fixes.

---

## 💡 Security Measures

Cherry Studio integrates several security best practices, including:

- Strict dependency updates and regular vulnerability scanning.
- TypeScript strict mode and linting to reduce potential injection or runtime issues.
- Enforced code formatting and pre-commit hooks.
- Internal security reviews before releases.
- Dedicated MCP (Model Context Protocol) safeguards for model interactions and data privacy.

---

## 🛡️ Disclosure Policy

- We follow a **coordinated disclosure** approach.
- We will not publicly disclose vulnerabilities until a fix has been developed and released.
- Credit will be given to researchers who responsibly disclose vulnerabilities, if requested.

---

## 🤝 Acknowledgements

We greatly appreciate contributions from the security community and strive to recognize all researchers who help keep Cherry Studio safe.

---

## 🌟 Questions?

For any security-related questions not involving vulnerabilities, please reach out to:  
**<EMAIL>**

---

Thank you for helping keep Cherry Studio and its users secure!
