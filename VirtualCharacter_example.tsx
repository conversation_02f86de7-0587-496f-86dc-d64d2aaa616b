import React, { useEffect, useRef, useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import styled from 'styled-components'
import { Button } from 'antd'
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons'

import { live2DService } from '@renderer/services/Live2DService'
import { EventEmitter, EVENT_NAMES } from '@renderer/services/EventService'
import { loggerService } from '@logger'
import type { RootState } from '@renderer/store'

// 初始化日志
const logger = loggerService.initWindowSource('MiniWindow').withContext('VirtualCharacter')

// 样式组件
const Container = styled.div<{ $visible: boolean }>`
  position: relative
  width: 100%;
  height: 100%;
  background: transparent;
  transition: opacity 0.3s ease;
  opacity: ${props => props.$visible ? 1 : 0};
  pointer-events: ${props => props.$visible ? 'auto' : 'none'};
`

const Live2DContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
`

const ControlPanel = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  z-index: 10;
`

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  z-index: 20;
`

const ErrorOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff4d4f;
  font-size: 12px;
  text-align: center;
  padding: 20px;
  z-index: 20;
`

// 组件属性接口
interface VirtualCharacterProps {
  className?: string
}

/**
 * 虚拟角色组件 - 在迷你窗口中显示 Live2D 角色
 * 
 * 功能：
 * 1. 初始化和管理 Live2D 服务
 * 2. 响应用户交互（显示/隐藏、动画控制）
 * 3. 处理加载状态和错误状态
 * 4. 与 Redux 状态同步
 */
export const VirtualCharacter: React.FC<VirtualCharacterProps> = ({ className }) => {
  // Redux 状态
  const dispatch = useDispatch()
  const {
    isVisible,
    currentModel,
    isLoading,
    error,
    animations
  } = useSelector((state: RootState) => state.virtualCharacter)

  // 本地状态
  const [isInitialized, setIsInitialized] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // 组件初始化
  useEffect(() => {
    initializeVirtualCharacter()
    setupEventListeners()

    return () => {
      cleanup()
    }
  }, [])

  // 监听可见性变化
  useEffect(() => {
    if (isVisible && !isInitialized) {
      initializeVirtualCharacter()
    }
  }, [isVisible, isInitialized])

  /**
   * 初始化虚拟角色
   */
  const initializeVirtualCharacter = async () => {
    try {
      logger.info('开始初始化虚拟角色组件')
      
      // 初始化 Live2D 服务
      await live2DService.initialize()
      
      setIsInitialized(true)
      logger.info('虚拟角色组件初始化完成')
      
    } catch (error) {
      logger.error('虚拟角色组件初始化失败', error as Error)
      // 错误已经在服务层处理，这里只需要记录日志
    }
  }

  /**
   * 设置事件监听器
   */
  const setupEventListeners = () => {
    // 监听 Live2D 服务事件
    EventEmitter.on(EVENT_NAMES.LIVE2D_READY, handleLive2DReady)
    EventEmitter.on(EVENT_NAMES.LIVE2D_ERROR, handleLive2DError)
    EventEmitter.on(EVENT_NAMES.LIVE2D_ANIMATION_COMPLETE, handleAnimationComplete)

    // 监听应用事件，触发相应的角色反应
    EventEmitter.on(EVENT_NAMES.AI_RESPONSE_RECEIVED, handleAIResponse)
    EventEmitter.on(EVENT_NAMES.SEND_MESSAGE, handleMessageSent)
  }

  /**
   * Live2D 准备就绪处理
   */
  const handleLive2DReady = () => {
    logger.info('Live2D 服务准备就绪')
    // 可以在这里添加一些初始化动画
    live2DService.playAnimation({ animation: 'idle', loop: true })
  }

  /**
   * Live2D 错误处理
   */
  const handleLive2DError = (errorInfo: any) => {
    logger.error('Live2D 服务发生错误', errorInfo)
    // 错误状态已经在服务层更新到 Redux，这里可以添加额外的错误处理逻辑
  }

  /**
   * 动画完成处理
   */
  const handleAnimationComplete = (animationOptions: any) => {
    logger.debug('Live2D 动画播放完成', animationOptions)
  }

  /**
   * AI 响应处理
   */
  const handleAIResponse = () => {
    if (isVisible && currentModel) {
      // AI 回复时播放开心动画
      live2DService.playAnimation({ 
        animation: 'happy', 
        loop: false,
        fadeInTime: 300,
        fadeOutTime: 300
      })
    }
  }

  /**
   * 消息发送处理
   */
  const handleMessageSent = () => {
    if (isVisible && currentModel) {
      // 用户发送消息时播放思考动画
      live2DService.playAnimation({ 
        animation: 'idle', 
        loop: true 
      })
    }
  }

  /**
   * 切换可见性
   */
  const toggleVisibility = () => {
    const newVisibility = !isVisible
    live2DService.setVisible(newVisibility)
    logger.info(`用户${newVisibility ? '显示' : '隐藏'}虚拟角色`)
  }

  /**
   * 播放随机动画
   */
  const playRandomAnimation = () => {
    if (animations.length > 0) {
      const randomAnimation = animations[Math.floor(Math.random() * animations.length)]
      live2DService.playAnimation({ 
        animation: randomAnimation, 
        loop: false 
      })
      logger.debug('播放随机动画', { animation: randomAnimation })
    }
  }

  /**
   * 组件清理
   */
  const cleanup = () => {
    logger.info('清理虚拟角色组件')
    
    // 移除事件监听器
    EventEmitter.off(EVENT_NAMES.LIVE2D_READY, handleLive2DReady)
    EventEmitter.off(EVENT_NAMES.LIVE2D_ERROR, handleLive2DError)
    EventEmitter.off(EVENT_NAMES.LIVE2D_ANIMATION_COMPLETE, handleAnimationComplete)
    EventEmitter.off(EVENT_NAMES.AI_RESPONSE_RECEIVED, handleAIResponse)
    EventEmitter.off(EVENT_NAMES.SEND_MESSAGE, handleMessageSent)
  }

  // 渲染加载状态
  if (isLoading) {
    return (
      <Container $visible={true} className={className}>
        <LoadingOverlay>
          正在加载虚拟角色...
        </LoadingOverlay>
      </Container>
    )
  }

  // 渲染错误状态
  if (error) {
    return (
      <Container $visible={true} className={className}>
        <ErrorOverlay>
          <div>
            <div>虚拟角色加载失败</div>
            <div style={{ fontSize: '10px', marginTop: '8px', opacity: 0.7 }}>
              {error}
            </div>
            <Button 
              size="small" 
              type="primary" 
              style={{ marginTop: '12px' }}
              onClick={initializeVirtualCharacter}
            >
              重试
            </Button>
          </div>
        </ErrorOverlay>
      </Container>
    )
  }

  return (
    <Container $visible={isVisible} className={className} ref={containerRef}>
      {/* 控制面板 */}
      <ControlPanel>
        <Button
          size="small"
          type="text"
          icon={isVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
          onClick={toggleVisibility}
          title={isVisible ? '隐藏角色' : '显示角色'}
        />
        {isVisible && currentModel && (
          <Button
            size="small"
            type="text"
            onClick={playRandomAnimation}
            title="播放随机动画"
          >
            🎭
          </Button>
        )}
      </ControlPanel>

      {/* Live2D 渲染容器 */}
      {isVisible && currentModel && (
        <Live2DContainer>
          {/* 这里应该放置实际的 Live2D 渲染组件 */}
          <div style={{ 
            width: '100%', 
            height: '100%', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            color: '#666',
            fontSize: '12px'
          }}>
            Live2D 模型渲染区域
            <br />
            模型: {currentModel.name}
          </div>
        </Live2DContainer>
      )}
    </Container>
  )
}

export default VirtualCharacter
