<div align="right" >
  <details>
    <summary >🌐 Language</summary>
    <div>
      <div align="right">
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=en">English</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=zh-CN">简体中文</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=zh-TW">繁體中文</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=ja">日本語</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=ko">한국어</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=hi">हिन्दी</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=th">ไทย</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=fr">Français</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=de">Deutsch</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=es">Español</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=it">Itapano</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=ru">Русский</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=pt">Português</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=nl">Nederlands</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=pl">Polski</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=ar">العربية</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=fa">فارسی</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=tr">Türkçe</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=vi">Tiếng Việt</a></p>
        <p><a href="https://openaitx.github.io/view.html?user=CherryHQ&project=cherry-studio&lang=id">Bahasa Indonesia</a></p>
      </div>
    </div>
  </details>
</div>

<h1 align="center">
  <a href="https://github.com/CherryHQ/cherry-studio/releases">
    <img src="https://github.com/CherryHQ/cherry-studio/blob/main/build/icon.png?raw=true" width="150" height="150" alt="banner" /><br>
  </a>
</h1>
<p align="center">
  <a href="https://github.com/CherryHQ/cherry-studio">English</a> | 中文 | <a href="https://cherry-ai.com">官方网站</a> | <a href="https://docs.cherry-ai.com/cherry-studio-wen-dang/zh-cn">文档</a> | <a href="./dev.md">开发</a> | <a href="https://github.com/CherryHQ/cherry-studio/issues">反馈</a><br>
</p>

<!-- 题头徽章组合 -->

<div align="center">

[![][deepwiki-shield]][deepwiki-link]
[![][twitter-shield]][twitter-link]
[![][discord-shield]][discord-link]
[![][telegram-shield]][telegram-link]

</div>

<div align="center">

[![][github-release-shield]][github-release-link]
[![][github-contributors-shield]][github-contributors-link]
[![][license-shield]][license-link]
[![][commercial-shield]][commercial-link]
[![][sponsor-shield]][sponsor-link]

</div>

<div align="center">
 <a href="https://hellogithub.com/repository/1605492e1e2a4df3be07abfa4578dd37" target="_blank" style="text-decoration: none"><img src="https://api.hellogithub.com/v1/widgets/recommend.svg?rid=1605492e1e2a4df3be07abfa4578dd37" alt="Featured｜HelloGitHub"  width="220" height="55" /></a>
 <a href="https://trendshift.io/repositories/11772" target="_blank" style="text-decoration: none"><img src="https://trendshift.io/api/badge/repositories/11772" alt="kangfenmao%2Fcherry-studio | Trendshift" width="220" height="55" /></a>
 <a href="https://www.producthunt.com/posts/cherry-studio?embed=true&utm_source=badge-featured&utm_medium=badge&utm_souce=badge-cherry&#0045;studio" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=496640&theme=light" alt="Cherry&#0032;Studio - AI&#0032;Chatbots&#0044;&#0032;AI&#0032;Desktop&#0032;Client | Product Hunt" width="220" height="55" /></a>
</div>

# 🍒 Cherry Studio

Cherry Studio 是一款支持多个大语言模型（LLM）服务商的桌面客户端，兼容 Windows、Mac 和 Linux 系统。

👏 欢迎加入 [Telegram 群组](https://t.me/CherryStudioAI)｜[Discord](https://discord.gg/wez8HtpxqQ) | [QQ群(575014769)](https://qm.qq.com/q/lo0D4qVZKi)

❤️ 喜欢 Cherry Studio? 点亮小星星 🌟 或 [赞助开发者](sponsor.md)! ❤️

# 📖 使用教程

https://docs.cherry-ai.com

# 🌠 界面

![](https://github.com/user-attachments/assets/36dddb2c-e0fb-4a5f-9411-91447bab6e18)

![](https://github.com/user-attachments/assets/f549e8a0-2385-40b4-b52b-2039e39f2930)

![](https://github.com/user-attachments/assets/58e0237c-4d36-40de-b428-53051d982026)

# 🌟 主要特性

1. **多样化 LLM 服务支持**：

- ☁️ 支持主流 LLM 云服务：OpenAI、Gemini、Anthropic、硅基流动等
- 🔗 集成流行 AI Web 服务：Claude、Peplexity、Poe、腾讯元宝、知乎直答等
- 💻 支持 Ollama、LM Studio 本地模型部署

2. **智能助手与对话**：

- 📚 内置 300+ 预配置 AI 助手
- 🤖 支持自定义创建专属助手
- 💬 多模型同时对话，获得多样化观点

3. **文档与数据处理**：

- 📄 支持文本、图片、Office、PDF 等多种格式
- ☁️ WebDAV 文件管理与数据备份
- 📊 Mermaid 图表可视化
- 💻 代码高亮显示

4. **实用工具集成**：

- 🔍 全局搜索功能
- 📝 话题管理系统
- 🔤 AI 驱动的翻译功能
- 🎯 拖拽排序
- 🔌 小程序支持
- ⚙️ MCP(模型上下文协议) 服务

5. **优质使用体验**：

- 🖥️ Windows、Mac、Linux 跨平台支持
- 📦 开箱即用，无需配置环境
- 🎨 支持明暗主题与透明窗口
- 📝 完整的 Markdown 渲染
- 🤲 便捷的内容分享功能

# 📝 开发计划

我们正在积极开发以下功能和改进：

1. 🎯 **核心功能**

- 选择助手 - 智能内容选择增强
- 深度研究 - 高级研究能力
- 全局记忆 - 全局上下文感知
- 文档预处理 - 改进文档处理能力
- MCP 市场 - 模型上下文协议生态系统

2. 🗂 **知识管理**

- 笔记与收藏功能
- 动态画布可视化
- OCR 光学字符识别
- TTS 文本转语音支持

3. 📱 **平台支持**

- 鸿蒙版本 (PC)
- Android 应用（第一期）
- iOS 应用（第一期）
- 多窗口支持
- 窗口置顶功能

4. 🔌 **高级特性**

- 插件系统
- ASR 语音识别
- 助手与话题交互重构

在我们的[项目面板](https://github.com/orgs/CherryHQ/projects/7)上跟踪进展并参与贡献。

想要影响开发计划？欢迎加入我们的 [GitHub 讨论区](https://github.com/CherryHQ/cherry-studio/discussions) 分享您的想法和反馈！

# 🌈 主题

- 主题库：https://cherrycss.com
- Aero 主题：https://github.com/hakadao/CherryStudio-Aero
- PaperMaterial 主题：https://github.com/rainoffallingstar/CherryStudio-PaperMaterial
- 仿 Claude 主题：https://github.com/bjl101501/CherryStudio-Claudestyle-dynamic
- 霓虹枫叶主题：https://github.com/BoningtonChen/CherryStudio_themes

欢迎 PR 更多主题

# 🤝 贡献

我们欢迎对 Cherry Studio 的贡献！您可以通过以下方式贡献：

1. **贡献代码**：开发新功能或优化现有代码
2. **修复错误**：提交您发现的错误修复
3. **维护问题**：帮助管理 GitHub 问题
4. **产品设计**：参与设计讨论
5. **撰写文档**：改进用户手册和指南
6. **社区参与**：加入讨论并帮助用户
7. **推广使用**：宣传 Cherry Studio

参考[分支策略](branching-strategy-zh.md)了解贡献指南

## 入门

1. **Fork 仓库**：Fork 并克隆到您的本地机器
2. **创建分支**：为您的更改创建分支
3. **提交更改**：提交并推送您的更改
4. **打开 Pull Request**：描述您的更改和原因

有关更详细的指南，请参阅我们的 [贡献指南](CONTRIBUTING.zh.md)

感谢您的支持和贡献！

# 🔧 开发者共创计划

我们正在启动 Cherry Studio 开发者共创计划，旨在为开源生态系统构建一个健康、正向反馈的循环。我们相信，优秀的软件是通过协作构建的，每一个合并的拉取请求都为项目注入新的生命力。

我们诚挚地邀请您加入我们的贡献者队伍，与我们一起塑造 Cherry Studio 的未来。

## 贡献者奖励计划

为了回馈我们的核心贡献者并创造良性循环，我们建立了以下长期激励计划。

**该计划的首个跟踪周期将是 2025 年第三季度（7月、8月、9月）。此周期的奖励将在 10月1日 发放。**

在任何跟踪周期内（例如，首个周期的 7月1日 至 9月30日），任何为 Cherry Studio 在 GitHub 上的开源项目贡献超过 **30 个有意义提交** 的开发者都有资格获得以下福利：

- **Cursor 订阅赞助**：获得 **70 美元** 的 [Cursor](https://cursor.sh/) 订阅积分或报销，让 AI 成为您最高效的编码伙伴。
- **无限模型访问**：获得 **DeepSeek** 和 **Qwen** 模型的 **无限次** API 调用。
- **前沿技术访问**：享受偶尔的特殊福利，包括 **Claude**、**Gemini** 和 **OpenAI** 等模型的 API 访问权限，让您始终站在技术前沿。

## 共同成长与未来规划

活跃的社区是任何可持续开源项目背后的推动力。随着 Cherry Studio 的发展，我们的奖励计划也将随之发展。我们致力于持续将我们的福利与行业内最优秀的工具和资源保持一致。这确保我们的核心贡献者获得有意义的支持，创造一个开发者、社区和项目共同成长的正向循环。

**展望未来，该项目还将采取越来越开放的态度来回馈整个开源社区。**

## 如何开始？

我们期待您的第一个拉取请求！

您可以从探索我们的仓库开始，选择一个 `good first issue`，或者提出您自己的改进建议。每一个提交都是开源精神的体现。

感谢您的关注和贡献。

让我们一起建设。

# 🏢 企业版

在社区版的基础上，我们自豪地推出 **Cherry Studio 企业版**——一个为现代团队和企业设计的私有部署 AI 生产力与管理平台。

企业版通过集中管理 AI 资源、知识和数据，解决了团队协作中的核心挑战。它赋能组织提升效率、促进创新并确保合规，同时在安全环境中保持对数据的 100% 控制。

## 核心优势

- **统一模型管理**：集中整合和管理各种基于云的大语言模型（如 OpenAI、Anthropic、Google Gemini）和本地部署的私有模型。员工可以开箱即用，无需单独配置。
- **企业级知识库**：构建、管理和分享全团队的知识库。确保知识得到保留且一致，使团队成员能够基于统一准确的信息与 AI 交互。
- **细粒度访问控制**：通过统一的管理后台轻松管理员工账户，并为不同模型、知识库和功能分配基于角色的权限。
- **完全私有部署**：在您的本地服务器或私有云上部署整个后端服务，确保您的数据 100% 私有且在您的控制之下，满足最严格的安全和合规标准。
- **可靠的后端服务**：提供稳定的 API 服务、企业级数据备份和恢复机制，确保业务连续性。

## ✨ 在线演示

> 🚧 **公开测试版通知**
>
> 企业版目前处于早期公开测试阶段，我们正在积极迭代和优化其功能。我们知道它可能还不够完全稳定。如果您在试用过程中遇到任何问题或有宝贵建议，我们非常感谢您能通过邮件联系我们提供反馈。

**🔗 [Cherry Studio 企业版](https://www.cherry-ai.com/enterprise)**

## 版本对比

| 功能         | 社区版                  | 企业版                                                                                         |
| :----------- | :---------------------- | :--------------------------------------------------------------------------------------------- |
| **开源**     | ✅ 是                   | ⭕️ 部分开源，对客户开放                                                                        |
| **成本**     | 个人使用免费 / 商业授权 | 买断 / 订阅费用                                                                                |
| **管理后台** | —                       | ● 集中化**模型**访问<br>● **员工**管理<br>● 共享**知识库**<br>● **访问**控制<br>● **数据**备份 |
| **服务器**   | —                       | ✅ 专用私有部署                                                                                |

## 获取企业版

我们相信企业版将成为您团队的 AI 生产力引擎。如果您对 Cherry Studio 企业版感兴趣，希望了解更多信息、请求报价或安排演示，请联系我们。

- **商业咨询与购买**：
  **📧 [<EMAIL>](mailto:<EMAIL>)**

# 🔗 相关项目

- [one-api](https://github.com/songquanpeng/one-api)：LLM API 管理及分发系统，支持 OpenAI、Azure、Anthropic 等主流模型，统一 API 接口，可用于密钥管理与二次分发。

- [ublacklist](https://github.com/iorate/ublacklist)：屏蔽特定网站在 Google 搜索结果中显示

# 🚀 贡献者

<a href="https://github.com/CherryHQ/cherry-studio/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=CherryHQ/cherry-studio" />
</a>
<br /><br />

# 📊 GitHub 统计

![Stats](https://repobeats.axiom.co/api/embed/a693f2e5f773eed620f70031e974552156c7f397.svg 'Repobeats analytics image')

# ⭐️ Star 记录

<a href="https://www.star-history.com/#CherryHQ/cherry-studio&Date">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=CherryHQ/cherry-studio&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=CherryHQ/cherry-studio&type=Date" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=CherryHQ/cherry-studio&type=Date" />
 </picture>
</a>

<!-- Links & Images -->

[deepwiki-shield]: https://img.shields.io/badge/Deepwiki-CherryHQ-0088CC
[deepwiki-link]: https://deepwiki.com/CherryHQ/cherry-studio
[twitter-shield]: https://img.shields.io/badge/Twitter-CherryStudioApp-0088CC?logo=x
[twitter-link]: https://twitter.com/CherryStudioHQ
[discord-shield]: https://img.shields.io/badge/Discord-@CherryStudio-0088CC?logo=discord
[discord-link]: https://discord.gg/wez8HtpxqQ
[telegram-shield]: https://img.shields.io/badge/Telegram-@CherryStudioAI-0088CC?logo=telegram
[telegram-link]: https://t.me/CherryStudioAI

<!-- 项目统计徽章 -->

[github-release-shield]: https://img.shields.io/github/v/release/CherryHQ/cherry-studio
[github-release-link]: https://github.com/CherryHQ/cherry-studio/releases
[github-contributors-shield]: https://img.shields.io/github/contributors/CherryHQ/cherry-studio
[github-contributors-link]: https://github.com/CherryHQ/cherry-studio/graphs/contributors

<!-- 许可和赞助徽章 -->

[license-shield]: https://img.shields.io/badge/License-AGPLv3-important.svg?logo=gnu
[license-link]: https://www.gnu.org/licenses/agpl-3.0
[commercial-shield]: https://img.shields.io/badge/商用授权-联系-white.svg?logoColor=white&logo=telegram&color=blue
[commercial-link]: mailto:<EMAIL>?subject=商业授权咨询
[sponsor-shield]: https://img.shields.io/badge/赞助支持-FF6699.svg?logo=githubsponsors&logoColor=white
[sponsor-link]: https://github.com/CherryHQ/cherry-studio/blob/main/docs/sponsor.md
