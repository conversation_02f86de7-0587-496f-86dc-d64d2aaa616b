# Cherry Studio 记忆功能指南

## 功能介绍

Cherry Studio 的记忆功能是一个强大的工具，能够帮助 AI 助手记住对话中的重要信息、用户偏好和上下文。通过记忆功能，您的 AI 助手可以：

- 📝 **记住重要信息**：自动从对话中提取并存储关键事实和信息
- 🧠 **个性化响应**：基于存储的记忆提供更加个性化和相关的回答
- 🔍 **智能检索**：在需要时自动搜索相关记忆，增强对话的连贯性
- 👥 **多用户支持**：为不同用户维护独立的记忆上下文

记忆功能特别适用于需要长期保持上下文的场景，例如个人助手、客户服务、教育辅导等。

## 如何启用记忆功能

### 1. 全局配置（首次设置）

在使用记忆功能之前，您需要先进行全局配置：

1. 点击侧边栏的 **记忆** 图标（记忆棒图标）进入记忆管理页面
2. 点击右上角的 **更多** 按钮（三个点），选择 **设置**
3. 在设置弹窗中配置以下必要项：
   - **LLM 模型**：选择用于处理记忆的语言模型（推荐使用 GPT-4 或 Claude 等高级模型）
   - **嵌入模型**：选择用于生成向量嵌入的模型（如 text-embedding-3-small）
   - **嵌入维度**：输入嵌入模型的维度（通常为 1536）
4. 点击 **确定** 保存配置

> ⚠️ **注意**：嵌入模型和维度一旦设置后无法更改，请谨慎选择。

### 2. 为助手启用记忆

完成全局配置后，您可以为特定助手启用记忆功能：

1. 进入 **助手** 页面
2. 选择要启用记忆的助手，点击 **编辑**
3. 在助手设置中找到 **记忆** 部分
4. 打开记忆功能开关
5. 保存助手设置

启用后，该助手将在对话过程中自动提取和使用记忆。

## 使用方法

### 查看记忆

1. 点击侧边栏的 **记忆** 图标进入记忆管理页面
2. 您可以看到所有存储的记忆卡片，包括：
   - 记忆内容
   - 创建时间
   - 所属用户

### 添加记忆

手动添加记忆有两种方式：

**方式一：在记忆管理页面添加**

1. 点击右上角的 **添加记忆** 按钮
2. 在弹窗中输入记忆内容
3. 点击 **添加** 保存

**方式二：在对话中自动提取**

- 当助手启用记忆功能后，系统会自动从对话中提取重要信息并存储为记忆

### 编辑记忆

1. 在记忆卡片上点击 **更多** 按钮（三个点）
2. 选择 **编辑**
3. 修改记忆内容
4. 点击 **保存**

### 删除记忆

1. 在记忆卡片上点击 **更多** 按钮
2. 选择 **删除**
3. 确认删除操作

## 记忆搜索

记忆管理页面提供了强大的搜索功能：

1. 在页面顶部的搜索框中输入关键词
2. 系统会实时过滤显示匹配的记忆
3. 搜索支持模糊匹配，可以搜索记忆内容的任何部分

## 用户管理

记忆功能支持多用户，您可以为不同的用户维护独立的记忆库：

### 切换用户

1. 在记忆管理页面，点击右上角的用户选择器
2. 选择要切换到的用户
3. 页面会自动加载该用户的记忆

### 添加新用户

1. 点击用户选择器
2. 选择 **添加新用户**
3. 输入用户 ID（支持字母、数字、下划线和连字符）
4. 点击 **添加**

### 删除用户

1. 切换到要删除的用户
2. 点击右上角的 **更多** 按钮
3. 选择 **删除用户**
4. 确认删除（注意：这将删除该用户的所有记忆）

> 💡 **提示**：默认用户（default-user）无法删除。

## 设置说明

### LLM 模型

- 用于处理记忆提取和更新的语言模型
- 建议选择能力较强的模型以获得更好的记忆提取效果
- 可随时更改

### 嵌入模型

- 用于将文本转换为向量，支持语义搜索
- 一旦设置后无法更改（为了保证现有记忆的兼容性）
- 推荐使用 OpenAI 的 text-embedding 系列模型

### 嵌入维度

- 嵌入向量的维度，需要与选择的嵌入模型匹配
- 常见维度：
  - text-embedding-3-small: 1536
  - text-embedding-3-large: 3072
  - text-embedding-ada-002: 1536

### 自定义提示词（可选）

- **事实提取提示词**：自定义如何从对话中提取信息
- **记忆更新提示词**：自定义如何更新现有记忆

## 最佳实践

### 1. 合理组织记忆

- 保持记忆简洁明了，每条记忆专注于一个具体信息
- 使用清晰的语言描述事实，避免模糊表达
- 定期审查和清理过时或不准确的记忆

### 2. 多用户场景

- 为不同的使用场景创建独立用户（如工作、个人、学习等）
- 使用有意义的用户 ID，便于识别和管理
- 定期备份重要用户的记忆数据

### 3. 模型选择建议

- **LLM 模型**：GPT-4、Claude 3 等高级模型能更准确地提取和理解信息
- **嵌入模型**：选择与您的主要使用语言匹配的模型

### 4. 性能优化

- 避免存储过多冗余记忆，这可能影响搜索性能
- 定期整理和合并相似的记忆
- 对于大量记忆的场景，考虑按主题或时间进行分类管理

## 常见问题

### Q: 为什么我无法启用记忆功能？

A: 请确保您已经完成全局配置，包括选择 LLM 模型和嵌入模型。

### Q: 记忆会自动同步到所有助手吗？

A: 不会。每个助手的记忆功能需要单独启用，且记忆是按用户隔离的。

### Q: 如何导出我的记忆数据？

A: 目前系统暂不支持直接导出功能，但所有记忆都存储在本地数据库中。

### Q: 删除的记忆可以恢复吗？

A: 删除操作是永久的，无法恢复。建议在删除前仔细确认。

### Q: 记忆功能会影响对话速度吗？

A: 记忆功能在后台异步处理，不会明显影响对话响应速度。但过多的记忆可能会略微增加搜索时间。

### Q: 如何清空所有记忆？

A: 您可以删除当前用户并重新创建，或者手动删除所有记忆条目。

## 注意事项

### 隐私保护

- 所有记忆数据都存储在您的本地设备上，不会上传到云端
- 请勿在记忆中存储敏感信息（如密码、私钥等）
- 定期审查记忆内容，确保没有意外存储的隐私信息

### 数据安全

- 记忆数据存储在本地数据库中
- 建议定期备份重要数据
- 更换设备时请注意迁移记忆数据

### 使用限制

- 单条记忆的长度建议不超过 500 字
- 每个用户的记忆数量建议控制在 1000 条以内
- 过多的记忆可能影响系统性能

## 技术细节

记忆功能使用了先进的 RAG（检索增强生成）技术：

1. **信息提取**：使用 LLM 从对话中智能提取关键信息
2. **向量化存储**：通过嵌入模型将文本转换为向量，支持语义搜索
3. **智能检索**：在对话时自动搜索相关记忆，提供给 AI 作为上下文
4. **持续学习**：随着对话进行，不断更新和完善记忆库

---

💡 **提示**：记忆功能是 Cherry Studio 的高级特性，合理使用可以大大提升 AI 助手的智能程度和用户体验。如有更多问题，欢迎查阅文档或联系支持团队。
