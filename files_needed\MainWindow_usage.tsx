// src/renderer/src/windows/main/MainWindow.tsx
// 在主窗口中使用 Live2D 组件

import React, { useState } from 'react'
import { Button, Switch } from 'antd'
import { VirtualCharacter, live2DService } from '@renderer/components/Live2D'

const MainWindow: React.FC = () => {
  const [showLive2D, setShowLive2D] = useState(false)

  const handleToggleLive2D = (checked: boolean) => {
    setShowLive2D(checked)
  }

  const handlePlayGreeting = async () => {
    try {
      await live2DService.playAnimation({ name: 'Greeting', loop: false })
    } catch (error) {
      console.error('播放问候动画失败:', error)
    }
  }

  const handleSendMessage = async () => {
    try {
      await live2DService.sendMessage('你好，我是主窗口！')
    } catch (error) {
      console.error('发送消息失败:', error)
    }
  }

  return (
    <div className="main-window">
      <div className="main-content">
        {/* 主窗口内容 */}
        <h1>Cherry Studio 主窗口</h1>
        
        {/* Live2D 控制面板 */}
        <div className="live2d-controls">
          <Switch 
            checked={showLive2D}
            onChange={handleToggleLive2D}
            checkedChildren="显示Live2D"
            unCheckedChildren="隐藏Live2D"
          />
          
          <Button onClick={handlePlayGreeting}>
            播放问候
          </Button>
          
          <Button onClick={handleSendMessage}>
            发送消息
          </Button>
        </div>
      </div>

      {/* Live2D 虚拟角色 - 侧边栏模式 */}
      {showLive2D && (
        <div className="live2d-sidebar" style={{ width: '300px', height: '400px' }}>
          <VirtualCharacter 
            windowId="main-window"
            showControls={false} // 主窗口不显示内置控制按钮
            allowExpand={false}  // 主窗口不允许展开
            defaultVisible={true}
          />
        </div>
      )}
    </div>
  )
}

export default MainWindow
