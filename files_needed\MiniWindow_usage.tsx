// src/renderer/src/windows/mini/home/<USER>
// 在迷你窗口中使用 Live2D 组件

import React from 'react'
import { VirtualCharacter } from '@renderer/components/Live2D'

const HomeWindow: React.FC = () => {
  const handleVisibilityChange = (visible: boolean) => {
    console.log('迷你窗口 Live2D 可见性变化:', visible)
  }

  const handleExpandChange = (expanded: boolean) => {
    console.log('迷你窗口 Live2D 展开状态变化:', expanded)
  }

  return (
    <div className="mini-window">
      {/* 其他内容 */}
      
      {/* Live2D 虚拟角色 - 简单使用 */}
      <VirtualCharacter 
        windowId="mini-window"
        showControls={true}
        allowExpand={true}
        onVisibilityChange={handleVisibilityChange}
        onExpandChange={handleExpandChange}
      />
    </div>
  )
}

export default HomeWindow
