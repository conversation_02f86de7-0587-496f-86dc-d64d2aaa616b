// src/renderer/src/windows/settings/Live2DSettings.tsx
// 在设置窗口中使用 Live2D 组件进行预览和配置

import React, { useState } from 'react'
import { Card, Button, Select, Slider, Space } from 'antd'
import { VirtualCharacter, live2DService } from '@renderer/components/Live2D'

const { Option } = Select

const Live2DSettings: React.FC = () => {
  const [selectedAnimation, setSelectedAnimation] = useState('Idle')
  const [selectedExpression, setSelectedExpression] = useState('Default')
  const [selectedPose, setSelectedPose] = useState('Normal')

  const animations = [
    'Idle', 'Happy', 'Sad', 'Surprised', 'Angry', 
    'Thinking', 'Greeting', 'Tap', 'Tap@Body'
  ]

  const expressions = [
    'Default', 'Happy', 'Sad', 'Surprised', 'Angry',
    'Thinking', 'Confused', 'Excited', 'Sleepy', 'Wink'
  ]

  const poses = [
    'Normal', 'Thinking', 'Happy', 'Surprised', 
    'Greeting', 'Relaxed', 'Focused'
  ]

  const handlePlayAnimation = async () => {
    try {
      await live2DService.playAnimation({ 
        name: selectedAnimation, 
        loop: false 
      })
    } catch (error) {
      console.error('播放动画失败:', error)
    }
  }

  const handleSetExpression = async () => {
    try {
      await live2DService.setExpression(selectedExpression)
    } catch (error) {
      console.error('设置表情失败:', error)
    }
  }

  const handleSetPose = async () => {
    try {
      await live2DService.setPose(selectedPose)
    } catch (error) {
      console.error('设置姿势失败:', error)
    }
  }

  const handleTestMessage = async () => {
    try {
      await live2DService.sendMessage('这是一条测试消息！')
    } catch (error) {
      console.error('发送测试消息失败:', error)
    }
  }

  return (
    <div className="live2d-settings">
      <div className="settings-panel">
        <Card title="Live2D 设置" style={{ marginBottom: 16 }}>
          
          {/* 动画控制 */}
          <div className="setting-group">
            <h4>动画控制</h4>
            <Space>
              <Select 
                value={selectedAnimation}
                onChange={setSelectedAnimation}
                style={{ width: 120 }}
              >
                {animations.map(anim => (
                  <Option key={anim} value={anim}>{anim}</Option>
                ))}
              </Select>
              <Button onClick={handlePlayAnimation}>播放动画</Button>
            </Space>
          </div>

          {/* 表情控制 */}
          <div className="setting-group">
            <h4>表情控制</h4>
            <Space>
              <Select 
                value={selectedExpression}
                onChange={setSelectedExpression}
                style={{ width: 120 }}
              >
                {expressions.map(expr => (
                  <Option key={expr} value={expr}>{expr}</Option>
                ))}
              </Select>
              <Button onClick={handleSetExpression}>设置表情</Button>
            </Space>
          </div>

          {/* 姿势控制 */}
          <div className="setting-group">
            <h4>姿势控制</h4>
            <Space>
              <Select 
                value={selectedPose}
                onChange={setSelectedPose}
                style={{ width: 120 }}
              >
                {poses.map(pose => (
                  <Option key={pose} value={pose}>{pose}</Option>
                ))}
              </Select>
              <Button onClick={handleSetPose}>设置姿势</Button>
            </Space>
          </div>

          {/* 测试功能 */}
          <div className="setting-group">
            <h4>测试功能</h4>
            <Button onClick={handleTestMessage}>发送测试消息</Button>
          </div>

        </Card>
      </div>

      {/* Live2D 预览区域 */}
      <div className="preview-panel" style={{ width: '400px', height: '500px' }}>
        <Card title="Live2D 预览" style={{ height: '100%' }}>
          <VirtualCharacter 
            windowId="settings-preview"
            showControls={true}
            allowExpand={false}
            defaultVisible={true}
          />
        </Card>
      </div>
    </div>
  )
}

export default Live2DSettings
