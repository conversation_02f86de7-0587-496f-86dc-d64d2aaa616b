// src/renderer/src/components/Live2D/VirtualCharacter.tsx
// 作用：Live2D 的完整UI组件，包含渲染和交互逻辑

import React, { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import styled from 'styled-components'
import { Button } from 'antd'
import { EyeOutlined, EyeInvisibleOutlined, PlayCircleOutlined } from '@ant-design/icons'

import { live2DService } from '@renderer/services/Live2DService'
import { EventEmitter, EVENT_NAMES } from '@renderer/services/EventService'
import { loggerService } from '@logger'
import type { RootState } from '@renderer/store'

const logger = loggerService.withContext('VirtualCharacter')

const Container = styled.div<{ $visible: boolean }>`
  position: relative;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
  opacity: ${props => props.$visible ? 1 : 0};
  pointer-events: ${props => props.$visible ? 'auto' : 'none'};
`

const ControlPanel = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  z-index: 10;
`

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  z-index: 20;
`

const ErrorOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff4d4f;
  font-size: 12px;
  text-align: center;
  padding: 20px;
  z-index: 20;
`

const Live2DIframe = styled.iframe`
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
`

interface VirtualCharacterProps {
  className?: string
  isExpanded?: boolean
  onToggleExpand?: (expanded: boolean) => void
}

export const VirtualCharacter: React.FC<VirtualCharacterProps> = ({ 
  className, 
  isExpanded = false,
  onToggleExpand 
}) => {
  // 从 Redux 读取状态
  const {
    isVisible,
    currentModel,
    isLoading,
    error,
    currentAnimation
  } = useSelector((state: RootState) => state.virtualCharacter)

  // 本地UI状态
  const [isInitialized, setIsInitialized] = useState(false)
  const [iframeLoaded, setIframeLoaded] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // 初始化
  useEffect(() => {
    initializeVirtualCharacter()
    setupEventListeners()

    return () => {
      cleanup()
    }
  }, [])

  // 监听展开状态变化
  useEffect(() => {
    if (iframeLoaded && iframeRef.current) {
      sendToIframe('setExpanded', { expanded: isExpanded })
    }
  }, [isExpanded, iframeLoaded])

  // 监听动画变化
  useEffect(() => {
    if (currentAnimation && iframeLoaded && iframeRef.current) {
      sendToIframe('playMotion', { motionName: currentAnimation })
    }
  }, [currentAnimation, iframeLoaded])

  const initializeVirtualCharacter = async () => {
    try {
      logger.info('初始化虚拟角色组件')
      await live2DService.initialize()
      setIsInitialized(true)
    } catch (error) {
      logger.error('虚拟角色组件初始化失败', error as Error)
    }
  }

  const setupEventListeners = () => {
    EventEmitter.on(EVENT_NAMES.LIVE2D_READY, handleLive2DReady)
    EventEmitter.on(EVENT_NAMES.LIVE2D_ERROR, handleLive2DError)
    EventEmitter.on(EVENT_NAMES.AI_RESPONSE_RECEIVED, handleAIResponse)
    
    // 监听来自 iframe 的消息
    window.addEventListener('message', handleIframeMessage)
  }

  const handleLive2DReady = () => {
    logger.info('Live2D 服务准备就绪')
  }

  const handleLive2DError = (errorInfo: any) => {
    logger.error('Live2D 服务发生错误', errorInfo)
  }

  const handleAIResponse = (data: any) => {
    if (iframeLoaded && iframeRef.current) {
      sendToIframe('sendMessage', { message: '收到AI回复' })
    }
  }

  const handleIframeMessage = (event: MessageEvent) => {
    if (event.data?.source === 'live2d') {
      const { type, action, data } = event.data
      
      if (type === 'event') {
        switch (action) {
          case 'ready':
            setIframeLoaded(true)
            logger.info('Live2D iframe 准备就绪')
            break
          case 'error':
            logger.error('Live2D iframe 发生错误', data)
            break
          case 'animation-started':
            logger.debug('Live2D 动画开始', data)
            break
        }
      }
    }
  }

  const sendToIframe = (action: string, data: any = {}) => {
    if (iframeRef.current?.contentWindow) {
      iframeRef.current.contentWindow.postMessage({
        source: 'live2d-parent',
        action,
        ...data
      }, '*')
    }
  }

  const toggleVisibility = () => {
    live2DService.setVisible(!isVisible)
  }

  const toggleExpanded = () => {
    const newExpanded = !isExpanded
    onToggleExpand?.(newExpanded)
  }

  const playRandomAnimation = () => {
    const animations = ['Happy', 'Idle', 'Tap', 'Tap@Body']
    const randomAnimation = animations[Math.floor(Math.random() * animations.length)]
    live2DService.playAnimation({ name: randomAnimation, loop: false })
  }

  const cleanup = () => {
    EventEmitter.off(EVENT_NAMES.LIVE2D_READY, handleLive2DReady)
    EventEmitter.off(EVENT_NAMES.LIVE2D_ERROR, handleLive2DError)
    EventEmitter.off(EVENT_NAMES.AI_RESPONSE_RECEIVED, handleAIResponse)
    window.removeEventListener('message', handleIframeMessage)
  }

  // 加载状态
  if (isLoading) {
    return (
      <Container $visible={true} className={className}>
        <LoadingOverlay>正在加载虚拟角色...</LoadingOverlay>
      </Container>
    )
  }

  // 错误状态
  if (error) {
    return (
      <Container $visible={true} className={className}>
        <ErrorOverlay>
          <div>
            <div>虚拟角色加载失败</div>
            <div style={{ fontSize: '10px', marginTop: '8px', opacity: 0.7 }}>
              {error}
            </div>
            <Button 
              size="small" 
              type="primary" 
              style={{ marginTop: '12px' }}
              onClick={initializeVirtualCharacter}
            >
              重试
            </Button>
          </div>
        </ErrorOverlay>
      </Container>
    )
  }

  return (
    <Container $visible={isVisible} className={className}>
      {/* 控制面板 */}
      <ControlPanel>
        <Button
          size="small"
          type="text"
          icon={isVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
          onClick={toggleVisibility}
          title={isVisible ? '隐藏角色' : '显示角色'}
        />
        {isVisible && (
          <>
            <Button
              size="small"
              type="text"
              icon={<PlayCircleOutlined />}
              onClick={playRandomAnimation}
              title="播放随机动画"
            />
            <Button
              size="small"
              type="text"
              onClick={toggleExpanded}
              title={isExpanded ? '收起' : '展开'}
            >
              {isExpanded ? '📱' : '🖥️'}
            </Button>
          </>
        )}
      </ControlPanel>

      {/* Live2D 渲染区域 - 使用你现有的 HTML 实现 */}
      {isVisible && (
        <Live2DIframe 
          ref={iframeRef}
          src="./virtualCharacter/live2d/live2d.html"
          title="Live2D Virtual Character"
        />
      )}
    </Container>
  )
}

export default VirtualCharacter
