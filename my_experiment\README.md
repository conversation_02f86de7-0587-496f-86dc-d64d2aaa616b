# Live2D Hiyori 展示项目

这个项目展示了如何使用 `pixi-live2d-display` 库来展示 Live2D 模型。

## 文件说明

### HTML 文件

- `live2d_demo.html` - 完整版本，包含更多交互功能和美化界面
- `new.html` - 官方示例文件（参考用）

### 模型文件

- `hiyori_pro_zh/` - Hiyori Live2D 模型文件夹
  - `runtime/hiyori_pro_t11.model3.json` - 主模型配置文件
  - `runtime/hiyori_pro_t11.moc3` - 模型数据文件
  - `runtime/hiyori_pro_t11.2048/` - 纹理文件夹
  - `runtime/motion/` - 动作文件夹

## 使用方法

### 1. 本地服务器运行

由于浏览器的同源策略，需要通过本地服务器运行：

```bash
# 使用 Python 3
python -m http.server 8000

# 使用 Python 2
python -m SimpleHTTPServer 8000

# 使用 Node.js (需要安装 http-server)
npx http-server

# 使用 PHP
php -S localhost:8000
```

### 2. 访问页面

启动服务器后，在浏览器中访问：

- http://localhost:8000/basic_live2d.html
- http://localhost:8000/simple_live2d.html
- http://localhost:8000/live2d_demo.html

### 3. 交互方式

- **点击模型** - 播放点击动作
- **使用控制按钮** - 播放不同动作
- **鼠标移动** - 视线跟随（部分版本支持）

## 技术栈

- **PIXI.js** - 2D 渲染引擎
- **pixi-live2d-display** - Live2D 显示库
- **Live2D Cubism Core** - Live2D 核心库

## 模型信息

- **模型名称**: Hiyori Pro
- **版本**: Cubism 4.0
- **支持动作**:
  - Idle - 待机动作
  - Tap - 点击动作
  - Flick - 滑动动作
  - FlickDown - 向下滑动
  - FlickUp - 向上滑动
  - Tap@Body - 身体点击
  - Flick@Body - 身体滑动

## 故障排除

### 模型加载失败

1. 确保所有文件路径正确
2. 检查是否通过本地服务器运行
3. 查看浏览器控制台错误信息
4. 确认模型文件完整性

### 动作播放失败

1. 检查动作名称是否正确
2. 确认模型支持该动作
3. 查看控制台错误信息

### 性能问题

1. 降低模型缩放比例
2. 减少同时播放的动作数量
3. 检查设备性能和浏览器兼容性

## 开发说明

如果要修改或扩展功能：

1. **添加新动作**: 修改 `playMotion()` 函数中的动作名称
2. **调整模型大小**: 修改 `model.scale.set()` 参数
3. **改变模型位置**: 修改 `model.x` 和 `model.y` 值
4. **添加新交互**: 在模型上添加更多事件监听器

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 许可证

本项目仅用于学习和演示目的。Live2D 模型文件的使用请遵循相应的许可协议。
