<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Live2D Demo - <PERSON><PERSON><PERSON></title>
    <style>
      body {
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: 'Arial', sans-serif;
        overflow: hidden;
      }

      #canvas {
        display: block;
        width: 100vw;
        height: 100vh;
        cursor: pointer;
      }

      .info-panel {
        position: absolute;
        top: 20px;
        left: 20px;
        background: rgba(255, 255, 255, 0.9);
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        max-width: 300px;
        z-index: 100;
      }

      .info-panel h2 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 18px;
      }

      .info-panel p {
        margin: 5px 0;
        color: #666;
        font-size: 14px;
      }

      .controls {
        position: absolute;
        bottom: 20px;
        left: 20px;
        background: rgba(255, 255, 255, 0.9);
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        z-index: 100;
      }

      .controls button {
        margin: 5px;
        padding: 8px 15px;
        border: none;
        border-radius: 5px;
        background: #667eea;
        color: white;
        cursor: pointer;
        font-size: 12px;
        transition: background 0.3s;
      }

      .controls button:hover {
        background: #764ba2;
      }

      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 18px;
        text-align: center;
        z-index: 50;
      }

      .loading::after {
        content: '';
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
        margin-left: 10px;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <!-- 加载提示 -->
    <div class="loading" id="loading">正在加载 Live2D 模型...</div>

    <!-- 信息面板 -->
    <div class="info-panel">
      <h2>🌸 Hiyori Live2D Demo</h2>
      <p><strong>模型:</strong> hiyori_pro_zh</p>
      <p><strong>操作:</strong> 点击模型进行交互</p>
      <p><strong>技术:</strong> pixi-live2d-display</p>
    </div>

    <!-- 控制面板 -->
    <div class="controls">
      <button onclick="playRandomMotion()">随机动作</button>
      <button onclick="playIdleMotion()">待机动作</button>
      <button onclick="toggleScale()">缩放切换</button>
      <button onclick="resetPosition()">重置位置</button>
    </div>

    <!-- Canvas -->
    <canvas id="canvas"></canvas>

    <!-- 引入必要的库 -->
    <script src="https://cubism.live2d.com/sdk-web/cubismcore/live2dcubismcore.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/dylanNew/live2d/webgl/Live2D/lib/live2d.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/pixi.js@6.5.2/dist/browser/pixi.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/pixi-live2d-display/dist/index.min.js"></script>

    <script>
      // 全局变量
      let app
      let model
      let isLargeScale = false

      // 模型路径
      const modelPath = './hiyori_pro_zh/runtime/hiyori_pro_t11.model3.json'

      // 初始化函数
      async function init() {
        try {
          // 创建 PIXI 应用
          app = new PIXI.Application({
            view: document.getElementById('canvas'),
            autoStart: true,
            resizeTo: window,
            backgroundColor: 0x000000,
            backgroundAlpha: 0
          })

          // 加载 Live2D 模型
          model = await PIXI.live2d.Live2DModel.from(modelPath)

          // 添加模型到舞台
          app.stage.addChild(model)

          // 设置模型初始属性
          setupModel()

          // 添加交互事件
          setupInteraction()

          // 隐藏加载提示
          document.getElementById('loading').style.display = 'none'

          console.log('Live2D 模型加载成功！')
        } catch (error) {
          console.error('加载模型时出错:', error)
          document.getElementById('loading').innerHTML = '❌ 模型加载失败<br>请检查模型文件路径'
        }
      }

      // 设置模型属性
      function setupModel() {
        if (!model) return

        // 设置模型缩放
        model.scale.set(0.3)

        // 设置模型位置（居中）
        model.x = app.screen.width / 2
        model.y = app.screen.height / 2 + 100

        // 设置锚点为中心
        model.anchor.set(0.5, 0.5)

        // 启用交互
        model.interactive = true
        model.buttonMode = true
      }

      // 设置交互事件
      function setupInteraction() {
        if (!model) return

        // 点击事件
        model.on('pointerdown', onModelClick)

        // 鼠标移动事件（视线跟随）
        app.stage.interactive = true
        app.stage.on('pointermove', onMouseMove)

        // 窗口大小改变事件
        window.addEventListener('resize', onResize)
      }

      // 模型点击事件
      function onModelClick(event) {
        if (!model) return

        // 播放点击动作
        const motionGroups = ['Tap', 'Tap@Body']
        const randomGroup = motionGroups[Math.floor(Math.random() * motionGroups.length)]

        if (model.internalModel.motionManager) {
          model.internalModel.motionManager.startMotion(randomGroup, 0)
        }

        console.log('模型被点击了！播放动作:', randomGroup)
      }

      // 鼠标移动事件（简单的视线跟随）
      function onMouseMove(event) {
        if (!model || !model.internalModel) return

        const mouseX = event.data.global.x
        const mouseY = event.data.global.y

        // 计算相对于模型的鼠标位置
        const modelX = model.x
        const modelY = model.y

        const deltaX = (mouseX - modelX) / app.screen.width
        const deltaY = (mouseY - modelY) / app.screen.height

        // 设置视线参数（如果模型支持）
        try {
          if (model.internalModel.coreModel) {
            const paramAngleX = model.internalModel.coreModel.getParameterIndex('ParamAngleX')
            const paramAngleY = model.internalModel.coreModel.getParameterIndex('ParamAngleY')

            if (paramAngleX >= 0) {
              model.internalModel.coreModel.setParameterValueByIndex(paramAngleX, deltaX * 30)
            }
            if (paramAngleY >= 0) {
              model.internalModel.coreModel.setParameterValueByIndex(paramAngleY, deltaY * 30)
            }
          }
        } catch (e) {
          // 忽略参数设置错误
        }
      }

      // 窗口大小改变事件
      function onResize() {
        if (!model) return

        // 重新设置模型位置
        model.x = app.screen.width / 2
        model.y = app.screen.height / 2 + 100
      }

      // 控制函数
      function playRandomMotion() {
        if (!model || !model.internalModel.motionManager) return

        const motionGroups = ['Idle', 'Flick', 'FlickDown', 'FlickUp']
        const randomGroup = motionGroups[Math.floor(Math.random() * motionGroups.length)]

        model.internalModel.motionManager.startMotion(randomGroup, 0)
        console.log('播放随机动作:', randomGroup)
      }

      function playIdleMotion() {
        if (!model || !model.internalModel.motionManager) return

        model.internalModel.motionManager.startMotion('Idle', 0)
        console.log('播放待机动作')
      }

      function toggleScale() {
        if (!model) return

        isLargeScale = !isLargeScale
        const newScale = isLargeScale ? 0.5 : 0.3

        // 平滑缩放动画
        const startScale = model.scale.x
        const duration = 500 // 毫秒
        const startTime = Date.now()

        function animate() {
          const elapsed = Date.now() - startTime
          const progress = Math.min(elapsed / duration, 1)

          // 使用缓动函数
          const easeProgress = 1 - Math.pow(1 - progress, 3)
          const currentScale = startScale + (newScale - startScale) * easeProgress

          model.scale.set(currentScale)

          if (progress < 1) {
            requestAnimationFrame(animate)
          }
        }

        animate()
        console.log('切换缩放:', newScale)
      }

      function resetPosition() {
        if (!model) return

        // 重置位置和缩放
        model.x = app.screen.width / 2
        model.y = app.screen.height / 2 + 100
        model.scale.set(0.3)
        isLargeScale = false

        console.log('重置模型位置和缩放')
      }

      // 页面加载完成后初始化
      window.addEventListener('load', init)

      // 错误处理
      window.addEventListener('error', function (e) {
        console.error('页面错误:', e.error)
        document.getElementById('loading').innerHTML = '❌ 发生错误<br>请检查控制台'
      })
    </script>
  </body>
</html>
