@use './color.scss';
@use './font.scss';
@use './markdown.scss';
@use './ant.scss';
@use './scrollbar.scss';
@use './container.scss';
@use './animation.scss';
@import '../fonts/icon-fonts/iconfont.css';
@import '../fonts/ubuntu/ubuntu.css';
@import '../fonts/country-flag-fonts/flag.css';

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

*:focus {
  outline: none;
}

* {
  -webkit-tap-highlight-color: transparent;
}

html,
body,
#root {
  height: 100%;
  width: 100%;
  margin: 0;
}

#root {
  display: flex;
  flex-direction: row;
  flex: 1;
}

body {
  display: flex;
  min-height: 100vh;
  color: var(--color-text);
  font-size: 14px;
  line-height: 1.6;
  overflow: hidden;
  font-family: var(--font-family);
  text-rendering: optimizeLegibility;
  transition: background-color 0.3s linear;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

input,
textarea,
[contenteditable='true'],
.markdown,
#messages,
.selectable,
pre,
code {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

a {
  -webkit-user-drag: none;
}

ul {
  list-style: none;
}

.loader {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #000;
  box-shadow:
    32px 0 #000,
    -32px 0 #000;
  position: relative;
  animation: flash 0.5s ease-out infinite alternate;
}

.drag {
  -webkit-app-region: drag;
}

.nodrag {
  -webkit-app-region: no-drag;
}

.text-nowrap {
  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-wrap: break-word;
}

.bubble:not(.multi-select-mode) {
  .block-wrapper {
    display: flow-root;
  }

  .block-wrapper:last-child > *:last-child {
    margin-bottom: 0;
  }

  .message-content-container > *:last-child {
    margin-bottom: 0;
  }

  .message-thought-container {
    margin-top: 8px;
  }

  .message-user {
    .message-header {
      flex-direction: row-reverse;
      text-align: right;
      .message-header-info-wrap {
        flex-direction: row-reverse;
        text-align: right;
      }
    }
    .message-content-container {
      border-radius: 10px;
      padding: 10px 16px 10px 16px;
      background-color: var(--chat-background-user);
      align-self: self-end;
    }
    .MessageFooter {
      margin-top: 2px;
      align-self: self-end;
    }
  }

  .message-assistant {
    .message-content-container {
      padding-left: 0;
    }
    .MessageFooter {
      margin-left: 0;
    }
  }

  code {
    color: var(--color-text);
  }
  .markdown {
    display: flow-root;
    *:last-child {
      margin-bottom: 0;
    }
  }
}

.lucide:not(.lucide-custom) {
  color: var(--color-icon);
}

::highlight(search-matches) {
  background-color: var(--color-background-highlight);
  color: var(--color-highlight);
}

::highlight(current-match) {
  background-color: var(--color-background-highlight-accent);
}

textarea {
  &::-webkit-resizer {
    display: none;
  }
}
