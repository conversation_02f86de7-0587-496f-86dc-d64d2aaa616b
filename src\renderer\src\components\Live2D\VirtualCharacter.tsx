// src/renderer/src/components/Live2D/VirtualCharacter.tsx
// 可复用的 Live2D 组件，可在任何窗口中使用

import React, { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import styled from 'styled-components'
import { Button } from 'antd'
import { EyeOutlined, EyeInvisibleOutlined, PlayCircleOutlined, ExpandOutlined, CompressOutlined } from '@ant-design/icons'

import { live2DService } from '@renderer/services/Live2DService'
import { EventEmitter, EVENT_NAMES } from '@renderer/services/EventService'
import { loggerService } from '@logger'
import type { RootState } from '@renderer/store'

const logger = loggerService.withContext('VirtualCharacter')

const Container = styled.div<{ $visible: boolean; $isExpanded: boolean }>`
  position: relative;
  width: ${props => props.$isExpanded ? '100%' : '200px'};
  height: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: ${props => props.$visible ? 1 : 0};
  pointer-events: ${props => props.$visible ? 'auto' : 'none'};
  background: transparent;
  -webkit-app-region: no-drag;

  ${props => props.$isExpanded && `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    width: 100% !important;
  `}
`

const ControlPanel = styled.div`
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  z-index: 100;
`

const ControlButton = styled(Button)`
  background: rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  border-radius: 6px !important;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-app-region: no-drag;
  opacity: 0.8;

  &:hover {
    background: rgba(0, 0, 0, 0.2) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px);
    opacity: 1;
  }

  &:active {
    transform: translateY(0);
  }

  &:focus {
    outline: none !important;
    box-shadow: none !important;
  }
`

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-background);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text);
  font-size: 14px;
  z-index: 20;
  -webkit-app-region: no-drag;
`

const ErrorOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-background);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  padding: 20px;
  -webkit-app-region: no-drag;
`

const ErrorContent = styled.div`
  text-align: center;
  max-width: 80%;
  
  .error-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
  
  .error-title {
    color: var(--color-text);
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
  }
  
  .error-message {
    color: var(--color-error);
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 12px;
    word-break: break-word;
  }
  
  .error-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
  }
`

const Live2DIframe = styled.iframe`
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
`

interface VirtualCharacterProps {
  className?: string
  windowId?: string // 用于标识不同的窗口实例
  showControls?: boolean // 是否显示控制按钮
  allowExpand?: boolean // 是否允许展开
  defaultVisible?: boolean // 默认是否可见
  onVisibilityChange?: (visible: boolean) => void
  onExpandChange?: (expanded: boolean) => void
}

export const VirtualCharacter: React.FC<VirtualCharacterProps> = ({ 
  className,
  windowId = 'default',
  showControls = true,
  allowExpand = true,
  defaultVisible = true,
  onVisibilityChange,
  onExpandChange
}) => {
  // 从 Redux 读取全局状态
  const {
    isVisible,
    isExpanded,
    isLoading,
    isReady,
    error,
    fileUrl,
    currentAnimation
  } = useSelector((state: RootState) => state.virtualCharacter)

  // 本地UI状态
  const [localVisible, setLocalVisible] = useState(defaultVisible)
  const [iframeLoaded, setIframeLoaded] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // 计算实际的可见性（全局状态 && 本地状态）
  const actualVisible = isVisible && localVisible

  // 初始化
  useEffect(() => {
    const initializeComponent = async () => {
      try {
        logger.info(`初始化 Live2D 组件 [${windowId}]`)
        
        // 注册窗口
        live2DService.registerWindow(windowId)
        
        // 初始化服务
        await live2DService.initialize()
        
      } catch (error) {
        logger.error(`Live2D 组件初始化失败 [${windowId}]`, error as Error)
      }
    }

    initializeComponent()
    setupEventListeners()

    return () => {
      cleanup()
    }
  }, [windowId])

  // 监听展开状态变化，同步到 iframe
  useEffect(() => {
    if (iframeLoaded && iframeRef.current) {
      sendToIframe('setExpanded', { expanded: isExpanded })
    }
  }, [isExpanded, iframeLoaded])

  // 监听动画变化，同步到 iframe
  useEffect(() => {
    if (currentAnimation && iframeLoaded && iframeRef.current) {
      sendToIframe('playMotion', { motionName: currentAnimation })
    }
  }, [currentAnimation, iframeLoaded])

  const setupEventListeners = () => {
    // 监听来自 iframe 的消息
    window.addEventListener('message', handleIframeMessage)
    
    // 监听 Live2D 服务事件
    EventEmitter.on(EVENT_NAMES.LIVE2D_READY, handleLive2DReady)
    EventEmitter.on(EVENT_NAMES.LIVE2D_ERROR, handleLive2DError)
  }

  const handleLive2DReady = () => {
    logger.info(`Live2D 服务准备就绪 [${windowId}]`)
  }

  const handleLive2DError = (errorInfo: any) => {
    logger.error(`Live2D 服务发生错误 [${windowId}]`, errorInfo)
  }

  const handleIframeMessage = (event: MessageEvent) => {
    if (event.data?.source === 'live2d') {
      const { type, action, data } = event.data
      
      if (type === 'event') {
        switch (action) {
          case 'ready':
            setIframeLoaded(true)
            logger.info(`Live2D iframe 准备就绪 [${windowId}]`)
            break
          case 'error':
            logger.error(`Live2D iframe 发生错误 [${windowId}]`, data)
            break
          case 'animation-started':
            logger.debug(`Live2D 动画开始 [${windowId}]`, data)
            break
        }
      }
    }
  }

  const sendToIframe = (action: string, data: any = {}) => {
    if (iframeRef.current?.contentWindow) {
      iframeRef.current.contentWindow.postMessage({
        source: 'live2d-parent',
        action,
        ...data
      }, '*')
    }
  }

  const toggleLocalVisibility = () => {
    const newVisible = !localVisible
    setLocalVisible(newVisible)
    onVisibilityChange?.(newVisible)
    logger.info(`切换本地可见性 [${windowId}]`, { from: localVisible, to: newVisible })
  }

  const toggleExpanded = () => {
    if (!allowExpand) return
    
    const newExpanded = !isExpanded
    live2DService.setExpandedMode(newExpanded)
    onExpandChange?.(newExpanded)
    logger.info(`切换展开模式 [${windowId}]`, { from: isExpanded, to: newExpanded })
  }

  const playRandomAnimation = () => {
    const animations = ['Happy', 'Idle', 'Tap', 'Tap@Body', 'Greeting']
    const randomAnimation = animations[Math.floor(Math.random() * animations.length)]
    
    live2DService.playAnimation({ 
      name: randomAnimation, 
      loop: false 
    }).catch(error => {
      logger.error(`播放随机动画失败 [${windowId}]`, error)
    })
  }

  const retryInitialization = async () => {
    try {
      logger.info(`重试初始化 [${windowId}]`)
      await live2DService.initialize()
    } catch (error) {
      logger.error(`重试初始化失败 [${windowId}]`, error as Error)
    }
  }

  const cleanup = () => {
    logger.info(`清理 Live2D 组件 [${windowId}]`)
    
    // 注销窗口
    live2DService.unregisterWindow(windowId)
    
    // 移除事件监听器
    window.removeEventListener('message', handleIframeMessage)
    EventEmitter.off(EVENT_NAMES.LIVE2D_READY, handleLive2DReady)
    EventEmitter.off(EVENT_NAMES.LIVE2D_ERROR, handleLive2DError)
  }

  // 加载状态
  if (isLoading) {
    return (
      <Container $visible={true} $isExpanded={false} className={className}>
        <LoadingOverlay>正在加载虚拟角色...</LoadingOverlay>
      </Container>
    )
  }

  // 错误状态
  if (error) {
    return (
      <Container $visible={true} $isExpanded={false} className={className}>
        <ErrorOverlay>
          <ErrorContent>
            <div className="error-icon">❌</div>
            <div className="error-title">虚拟角色加载失败</div>
            <div className="error-message">{error}</div>
            <div className="error-actions">
              <Button size="small" type="primary" onClick={retryInitialization}>
                重试
              </Button>
            </div>
          </ErrorContent>
        </ErrorOverlay>
      </Container>
    )
  }

  return (
    <Container $visible={actualVisible} $isExpanded={isExpanded} className={className}>
      {/* 控制面板 */}
      {showControls && (
        <ControlPanel>
          <ControlButton
            size="small"
            type="text"
            icon={localVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
            onClick={toggleLocalVisibility}
            title={localVisible ? '隐藏角色' : '显示角色'}
          />
          {localVisible && (
            <>
              <ControlButton
                size="small"
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={playRandomAnimation}
                title="播放随机动画"
              />
              {allowExpand && (
                <ControlButton
                  size="small"
                  type="text"
                  icon={isExpanded ? <CompressOutlined /> : <ExpandOutlined />}
                  onClick={toggleExpanded}
                  title={isExpanded ? '收起' : '展开'}
                />
              )}
            </>
          )}
        </ControlPanel>
      )}

      {/* Live2D 渲染区域 */}
      {actualVisible && isReady && fileUrl && (
        <Live2DIframe 
          ref={iframeRef}
          src={fileUrl}
          title={`Live2D Virtual Character - ${windowId}`}
        />
      )}
    </Container>
  )
}

export default VirtualCharacter
