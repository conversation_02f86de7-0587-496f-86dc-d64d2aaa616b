{"translation": {"agents": {"add.button": "Προσθήκη στο Βοηθό", "add.knowledge_base": "Βάση γνώσεων", "add.knowledge_base.placeholder": "Επιλέξτε βάση γνώσεων", "add.name": "Όνομα", "add.name.placeholder": "Εισαγάγετε όνομα", "add.prompt": "Φράση προκαλέσεως", "add.prompt.placeholder": "Εισαγάγετε φράση προκαλέσεως", "add.prompt.variables.tip": {"content": "{{date}}:\tΗμερομηνία\n{{time}}:\tΏρα\n{{datetime}}:\tΗμερομηνία και ώρα\n{{system}}:\tΛειτουργικό σύστημα\n{{arch}}:\tΑρχιτεκτονική CPU\n{{language}}:\tΓλώσσ<PERSON>\n{{model_name}}:\tΌνομα μοντέλου\n{{username}}:\tΌνομα χρήστη", "title": "Διαθέσιμες μεταβλητές"}, "add.title": "Δημιουργ<PERSON>α νέου ειδικού", "add.unsaved_changes_warning": "Έχετε μη αποθηκευμένες αλλαγές, είστε βέβαιοι ότι θέλετε να κλείσετε;", "delete.popup.content": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτόν τον ειδικό;", "edit.model.select.title": "Επιλογή μοντέλου", "edit.title": "Επεξεργασία ειδικού", "export": {"agent": "Εξαγωγή υποκειμένου"}, "import": {"button": "Εισαγωγή", "error": {"fetch_failed": "Αποτυχία λήψης δεδομένων από το URL", "invalid_format": "Μη έγκυρη μορφή πράκτορα: λείπουν υποχρεωτικά πεδία", "url_required": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε τη διεύθυνση URL"}, "file_filter": "Αρχεία JSON", "select_file": "Επιλέξτε αρχείο", "title": "Εισαγωγή από το εξωτερικό", "type": {"file": "Αρχείο", "url": "URL"}, "url_placeholder": "Εισάγετε τη διεύθυνση URL JSON"}, "manage.title": "Διαχείριση ειδικών", "my_agents": "Οι ειδικοί μου", "search.no_results": "Δεν βρέθηκαν σχετικοί ειδικοί", "settings": {"title": "Διαμόρφωση Πράκτορα"}, "sorting.title": "Ταξινόμηση", "tag.agent": "Ειδικ<PERSON>ς", "tag.default": "Προεπιλογή", "tag.new": "<PERSON><PERSON><PERSON>", "tag.system": "Σύστημα", "title": "Ειδικοί"}, "assistants": {"abbr": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "clear.content": "Η διαγραφή του θέματος θα διαγράψει όλα τα θέματα και τα αρχεία του βοηθού. Είστε σίγουροι πως θέλετε να συνεχίσετε;", "clear.title": "Διαγρα<PERSON><PERSON> θέματος", "copy.title": "Αντιγραφή βοηθού", "delete.content": "Η διαγραφή του βοηθού θα διαγράψει όλα τα θέματα και τα αρχεία που είναι συνδεδεμένα με αυτόν. Είστε σίγουροι πως θέλετε να συνεχίσετε;", "delete.title": "Διαγρα<PERSON><PERSON> βοηθού", "edit.title": "Επεξεργασία βοηθού", "icon.type": "Εικόνα Βοηθού", "list": {"showByList": "Εμφάνιση με λίστα", "showByTags": "Εμφάνιση με ετικέτες"}, "save.success": "Η αποθήκευση ολοκληρώθηκε επιτυχώς", "save.title": "Αποθήκευση στον νοητή", "search": "Αναζήτηση βοηθού", "settings.default_model": "Προεπιλεγ<PERSON><PERSON>νο μοντέλο", "settings.knowledge_base": "Ρυθμίσεις βάσης γνώσεων", "settings.knowledge_base.recognition": "Κλήση βάσης γνώσης", "settings.knowledge_base.recognition.off": "Υποχρεωτική αναζήτηση", "settings.knowledge_base.recognition.on": "Αναγνώριση πρόθεσης", "settings.knowledge_base.recognition.tip": "Ο πράκτορας θα καλέσει τη δυνατότητα αναγνώρισης πρόθεσης του μεγάλου μοντέλου για να αποφασίσει αν χρειάζεται να κληθεί η βάση γνώσης για να απαντηθεί, και αυτή η λειτουργία θα εξαρτηθεί από τις δυνατότητες του μοντέλου", "settings.mcp": "Διακομιστής MCP", "settings.mcp.description": "Διακο<PERSON>ιστής MCP που είναι ενεργοποιημένος εξ ορισμού", "settings.mcp.enableFirst": "Πρώτα ενεργοποιήστε αυτόν τον διακομιστή στις ρυθμίσεις MCP", "settings.mcp.noServersAvailable": "Δεν υπάρχουν διαθέσιμοι διακομιστές MCP. Προσθέστε ένα διακομιστή στις ρυθμίσεις", "settings.mcp.title": "Ρυθμίσεις MCP", "settings.model": "Ρυθμίσεις μοντέλου", "settings.more": "Ρυθμίσεις Βοηθού", "settings.prompt": "Ρυθμίσεις προκαλύμματος", "settings.reasoning_effort": "<PERSON><PERSON><PERSON><PERSON> λογισμικού αλυσίδας", "settings.reasoning_effort.default": "Προεπιλογή", "settings.reasoning_effort.high": "Μεγάλο", "settings.reasoning_effort.low": "Μικρό", "settings.reasoning_effort.medium": "Μεσαίο", "settings.reasoning_effort.off": "Απενεργοποίηση", "settings.regular_phrases": {"add": "Προσθήκη φράσης", "contentLabel": "Περιεχόμενο", "contentPlaceholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το περιεχόμενο της φράσης. Υποστηρίζονται μεταβλητές, και στη συνέχεια πατήστε Tab για να μεταβείτε γρήγορα στη μεταβλητή και να την επεξεργαστείτε. Για παράδειγμα: \\nΒοήθησέ με να σχεδιάσω μια διαδρομή από το ${from} στο ${to}, και στη συνέχεια στείλε την στο ${email}.", "delete": "Διαγραφή φράσης", "deleteConfirm": "Είστε βέβ<PERSON><PERSON>ος ότι θέλετε να διαγράψετε αυτήν τη φράση;", "edit": "Επεξεργασία φράσης", "title": "Δημοφιλείς φράσεις", "titleLabel": "Τίτλος", "titlePlaceholder": "Εισαγάγετε τίτλο"}, "settings.title": "Ρυθμίσεις Βοηθού", "settings.tool_use_mode": "Τρό<PERSON>ος χρήσης εργαλείου", "settings.tool_use_mode.function": "Συνάρτηση", "settings.tool_use_mode.prompt": "Ερέθισμα", "tags": {"add": "Προσθήκη ετικέτας", "delete": "Διαγρα<PERSON><PERSON> ετικέτας", "deleteConfirm": "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτήν την ετικέτα;", "manage": "Διαχείριση ετικετών", "modify": "Επεξεργα<PERSON>ία ετικέτας", "none": "Δεν υπάρχουν προς το παρόν ετικέτες", "settings": {"title": "Ρυθμίσεις Ετικέτας"}, "untagged": "Αχαρα<PERSON>τ<PERSON><PERSON>ιστο"}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς"}, "auth": {"error": "Αποτυχ<PERSON><PERSON> στην αυτόματη πήγαινη των κλειδιών, παρακαλείστε να το κάνετε χειροκίνητα", "get_key": "Πήγαινη", "get_key_success": "Η αυτόματη πήγαινη των κλειδιών ήταν επιτυχής", "login": "Είσοδος", "oauth_button": "Είσοδος με {{provider}}"}, "backup": {"confirm": "Είστε σίγουροι ότι θέλετε να αντιγράψετε τα δεδομένα;", "confirm.button": "Επιλογή μοντέλου αντιγράφου προσωρινής αποθήκευσης", "content": "Αντιγράφετε όλα τα δεδομένα, συμπεριλαμβανομένων των εγγραφών συζήτησης, των ρυθμίσεων, της βάσης γνώσεων και όλων των δεδομένων. Παρακαλούμε σημειώστε ότι η διαδικασία αντιγράφου μπορεί να χρειαστεί λίγο χρόνο. Ευχαριστούμε για την υπομονή.", "progress": {"completed": "Η αντιγραφή ασφαλείας ολοκληρώθηκε", "compressing": "Συμπίεση αρχείων...", "copying_files": "Αντιγραφή αρχείων... {{progress}}%", "preparing": "Ετοιμασία αντιγράφου ασφαλείας...", "title": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON> αντιγράφου ασφαλείας", "writing_data": "Εγγραφή δεδομένων..."}, "title": "Αντιγρα<PERSON><PERSON> Δεδομένων"}, "button": {"add": "προσθέστε", "added": "προστέθηκε", "case_sensitive": "Διάκριση πεζών/κεφαλαίων", "collapse": "συμπεριλάβετε", "includes_user_questions": "Περιλαμβάνει ερωτήσεις χρήστη", "manage": "χειριστείτε", "select_model": "επιλογή μοντέλου", "show.all": "δείξτε όλα", "update_available": "Υπάρχει διαθέσιμη ενημέρωση", "whole_word": "Ταίριασμα ολόκληρης λέξης"}, "chat": {"add.assistant.title": "Προσθήκη βοηθού", "add.topic.title": "Δημιουργ<PERSON>α νέου θέματος", "artifacts.button.download": "Λή<PERSON>η", "artifacts.button.openExternal": "Άνοιγμα στο εξωτερικό περιηγητή", "artifacts.button.preview": "Προεπισκόπηση", "artifacts.preview.openExternal.error.content": "Σφάλμα κατά την άνοιγμα στο εξωτερικό περιηγητή", "assistant.search.placeholder": "Αναζήτηση", "deeply_thought": "Έχει βαθιά σκεφτεί (χρήση {{secounds}} δευτερόλεπτα)", "default.description": "Γεια σου, εί<PERSON><PERSON><PERSON> ο προεπαγγελμα<PERSON>ι<PERSON><PERSON>ς βοηθός. Μπορείς να ξεκινήσεις να μου μιλάς αμέσως.", "default.name": "Προεπαγγ<PERSON>λ<PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON> βοηθός", "default.topic.name": "Προεπαγγελματικ<PERSON>ς θέμα", "history": {"assistant_node": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "click_to_navigate": "Κάντε κλικ για να μεταβείτε στο αντίστοιχο μήνυμα", "coming_soon": "Το διάγραμμα ροής συνομιλίας θα είναι σύντομα διαθέσιμο", "no_messages": "Δεν βρέθηκαν μηνύματα", "start_conversation": "Ξεκινήστε μια συνομιλία για να δείτε το διάγραμμα ροής", "title": "Ιστορικ<PERSON> συνομιλιών", "user_node": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "view_full_content": "Προβολή πλήρους περιεχομένου"}, "input.auto_resize": "Αυτόματη μείωση ύψους", "input.clear": "Καθα<PERSON>ισμ<PERSON>ς μηνυμάτων {{Command}}", "input.clear.content": "Είσαι σίγουρος ότι θέλεις να διαγράψεις όλα τα μηνύματα της τρέχουσας συζήτησης;", "input.clear.title": "Καθαρισμός μηνυμάτων", "input.collapse": "Συμπιέζω", "input.context_count.tip": "Πλήθος ενδιάμεσων/Μέγιστο πλήθος ενδιάμεσων", "input.estimated_tokens.tip": "Εκτιμώμενος αριθμός tokens", "input.expand": "Επεκτάση", "input.file_error": "Σφάλ<PERSON><PERSON> κατά την επεξεργασία του αρχείου", "input.file_not_supported": "Το μοντέλο δεν υποστηρίζει αυτό το είδος αρχείων", "input.generate_image": "Δημιουργ<PERSON>α εικόνας", "input.generate_image_not_supported": "Το μοντέλο δεν υποστηρίζει τη δημιουργία εικόνων", "input.knowledge_base": "Βάση γνώσεων", "input.new.context": "Καθα<PERSON>ισ<PERSON><PERSON>ς ενδιάμεσων {{Command}}", "input.new_topic": "Νέο θέμα {{Command}}", "input.pause": "Παύση", "input.placeholder": "Εισάγετε μήνυμα εδώ...", "input.send": "Αποστολή", "input.settings": "Ρυθμίσεις", "input.thinking": "Σκέψη", "input.thinking.budget_exceeds_max": "Ο προϋπολογισμός σκέψης υπερβαίνει τον μέγιστο αριθμό token", "input.thinking.mode.custom": "Προσαρμοσμένο", "input.thinking.mode.custom.tip": "Ο μέγιστος αριθμός token που μπορεί να σκεφτεί το μοντέλο. Πρέπει να ληφθεί υπόψη το όριο πλαισίου του μοντέλου, διαφορετικά θα εμφανιστεί σφάλμα", "input.thinking.mode.default": "Προεπιλογή", "input.thinking.mode.default.tip": "Το μοντέλο θα αποφασίσει αυτόματα τον αριθμό token για σκέψη", "input.thinking.mode.tokens.tip": "Ορίστε τον αριθμό των token για τη σκέψη", "input.tools.collapse": "Σύμπτυξη", "input.tools.collapse_in": "Εισαγωγή σε σύμπτυξη", "input.tools.collapse_out": "Αφαίρεση από σύμπτυξη", "input.tools.expand": "Επέκταση", "input.topics": "Θέματα", "input.translate": "Μετάφραση στο {{target_language}}", "input.translating": "Μετάφραση...", "input.upload": "Φόρτωση εικόνας ή έγγραφου", "input.upload.document": "Φόρτωση έγγραφου (το μοντέλ<PERSON> δεν υποστηρίζει εικόνες)", "input.upload.upload_from_local": "Μεταφόρτωση αρχείου από τον υπολογιστή...", "input.url_context": "Περιεχόμ<PERSON><PERSON><PERSON> ιστοσελίδας", "input.web_search": "Ενεργοποίηση διαδικτυα<PERSON><PERSON>ς αναζήτησης", "input.web_search.builtin": "Ενσωματωμένη στο μοντέλο", "input.web_search.builtin.disabled_content": "Η τρέχουσα έκδοση του μοντέλου δεν υποστηρίζει τη δυνατότητα διαδικτυακής αναζήτησης", "input.web_search.builtin.enabled_content": "Χρήση της ενσωματωμένης δυνατότητας διαδικτυακής αναζήτησης του μοντέλου", "input.web_search.button.ok": "Πήγαινε στις ρυθμίσεις", "input.web_search.enable": "Ενεργοποίηση διαδικτυα<PERSON><PERSON>ς αναζήτησης", "input.web_search.enable_content": "Πρέπει να ελέγξετε τη σύνδεση με το διαδίκτυο στις ρυθμίσεις πρώτα", "input.web_search.no_web_search": "<PERSON><PERSON><PERSON><PERSON><PERSON> διαδίκτυο", "input.web_search.no_web_search.description": "Να μην ενεργοποιηθεί η δυνατότητα διαδικτυακής αναζήτησης", "input.web_search.settings": "Ρυθμίσεις αναζήτησης στο διαδίκτυο", "message.new.branch": "Διακοπή", "message.new.branch.created": "Νέα διακοπή δημιουργήθηκε", "message.new.context": "Καθαρισμ<PERSON>ς ενδιάμεσων", "message.quote": "Αναφορά", "message.regenerate.model": "Εναλλαγ<PERSON> μοντέλου", "message.useful": "Χρήσιμ<PERSON>", "multiple.select": "Πολλαπλή επιλογή", "multiple.select.empty": "Δεν έχει επιλεγεί κανένα μήνυμα", "navigation": {"bottom": "Επιστροφή στο κάτω μέρος", "close": "Κλείσιμο", "first": "Ήδη το πρώτο μήνυμα", "history": "Ιστορικό συνομιλίας", "last": "Ήδη το τελευταίο μήνυμα", "next": "Επόμενο μήνυμα", "prev": "Προηγούμενο μήνυμα", "top": "Επιστροφή στην κορυφή"}, "resend": "Ξαναστείλε", "save": "Αποθήκευση", "save.file.title": "Αποθήκευση σε τοπικό αρχείο", "save.knowledge": {"content.citation.description": "Περιλαμβάνει πληροφορίες ανα<PERSON><PERSON><PERSON><PERSON><PERSON> από αναζήτηση στο διαδίκτυο και από τη βάση γνώσεων", "content.citation.title": "Αναφορά", "content.code.description": "Περιλαμβάνει αυτόνομα τμήματα κώδικα", "content.code.title": "Τμήμα Κώδικα", "content.error.description": "Περιλαμβάνει πληροφορίες σφαλμάτων κατά την εκτέλεση", "content.error.title": "Σφάλμα", "content.file.description": "Περιλαμβάνει αρχεία ως συνημμένα", "content.file.title": "Αρχείο", "content.maintext.description": "Περιλαμβάνει το κύριο κείμενο", "content.maintext.title": "Κ<PERSON><PERSON><PERSON><PERSON>είμενο", "content.thinking.description": "Περιλαμβάνει τη διαδικασία σκέψης του μοντέλου", "content.thinking.title": "Σκέψη", "content.tool_use.description": "Περιλαμβάνει παραμέτρους κλήσης εργαλείων και αποτελέσματα εκτέλεσης", "content.tool_use.title": "Χρήση Εργαλείου", "content.translation.description": "Περιλαμβάνει το περιεχόμενο μετάφρασης", "content.translation.title": "Μετάφραση", "empty.no_content": "Αυτό το μήνυμα δεν έχει περιεχόμενο προς αποθήκευση", "empty.no_knowledge_base": "Δεν υπάρχει διαθέσιμη βάση γνώσεων προς το παρόν. Δημιουργήστε πρώτα μια βάση γνώσεων", "error.invalid_base": "Η επιλεγμένη βάση γνώσεων δεν έχει ρυθμιστεί σωστά", "error.no_content_selected": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε τουλάχιστον ένα περιεχόμενο", "error.save_failed": "Η αποθήκευση απέτυχε. Ελέγξτε τη ρύθμιση της βάσης γνώσεων", "select.base.placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε βάση γνώσεων", "select.base.title": "Επιλογή βάσης γνώσεων", "select.content.tip": "Έχουν επιλεγεί {{count}} στοιχεία περιεχομένου. Οι τύποι κειμένου θα συγχωνευθούν και αποθηκευτούν ως μια σημείωση", "select.content.title": "Επιλέξτε τους τύπους περιεχομένου που θέλετε να αποθηκεύσετε", "title": "Αποθήκευση στη βάση γνώσεων"}, "settings.code.title": "Ρυθμίσεις μπλοκ κώδικα", "settings.code_collapsible": "Οι κώδικες μπορούν να συμπιεζόνται", "settings.code_editor": {"autocompletion": "Αυτόματη Συμπλήρωση", "fold_gutter": "Δίπλωση Περιθωρίου", "highlight_active_line": "Επισήμανση Ενεργού Γραμμής", "keymap": "Συντομ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Πληκτρολογίου", "title": "Επεξε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ώδικα"}, "settings.code_execution": {"timeout_minutes": "<PERSON><PERSON><PERSON><PERSON><PERSON> λήξης", "timeout_minutes.tip": "<PERSON><PERSON><PERSON><PERSON><PERSON> λήξης εκτέλεσης κώδικα (λε<PERSON>τ<PERSON>)", "tip": "Στη γραμμή εργαλείων των εκτελέσιμων blocks κώδικα θα εμφανίζεται το κουμπί εκτέλεσης· προσέξτε να μην εκτελέσετε επικίνδυνο κώδικα!", "title": "Εκτέλεση Κώδικα"}, "settings.code_wrappable": "Οι κώδικες μπορούν να γράφονται σε διαφορετική γραμμή", "settings.context_count": "<PERSON><PERSON><PERSON><PERSON><PERSON> ενδιάμεσων", "settings.context_count.tip": "Πλήθος των μηνυμάτων που θα παραμείνουν στα ενδιάμεσα, ό<PERSON><PERSON> μεγαλύτερο είναι το αριθμός, τόσ<PERSON> μεγαλύτερο είναι το μήκος του ενδιάμεσου και τόσο περισσότερα tokens χρησιμοποιούνται. Συνομιλία συνήθως συνιστάται μεταξύ 5-10", "settings.max": "Όχι ορισμένο", "settings.max_tokens": "Ενεργοποίηση περιορισμού μήκους μηνύματος", "settings.max_tokens.confirm": "Ενεργοποίηση περιορισμού μήκους μηνύματος", "settings.max_tokens.confirm_content": "Μετά την ενεργοποίηση του περιορισμού μήκους μηνύματος, ο μέγιστος αριθμός των tokens που χρησιμοποιούνται κάθε φορά, θα επηρεάζει το μήκος της απάντησης. Πρέπει να το ρυθμίζετε βάσει των περιορισμών του πλαισίου του μοντέλου, διαφορετικά θα σφάλλεται.", "settings.max_tokens.tip": "Ο μέγιστος αριθμός των tokens που χρησιμοποιούνται κάθε φορά, θα επηρεάζει το μήκος της απάντησης. Πρέπει να το ρυθμίζετε βάσει των περιορισμών του πλαισίου του μοντέλου, διαφορετικά θα σφάλλεται.", "settings.reset": "Επαναφορά", "settings.set_as_default": "Εφαρμογή στον προεπαγγελματικό βοηθό", "settings.show_line_numbers": "Εμφάνιση αριθμού γραμμών στον κώδικα", "settings.temperature": "Θερμοκρασία μοντέλου", "settings.temperature.tip": "Ο αντικειμενικ<PERSON>ς βαθμός τυχαιότητας του μοντέλου στην παραγωγή κειμένου. Ο μεγαλύτερος αριθμός σημαίνει περισσότερη ποικιλία, δημιουργικότητα και τυχαιότητα στις απαντήσεις· η έδρα μετά την επιλογή 0 επιστρέφει απαντήσεις βάσει των γεγονότων. Για καθημερινές συζητήσεις προτείνεται η επιλογή 0.7.", "settings.thought_auto_collapse": "Αυτόματη συμπίεση σκέψεων", "settings.thought_auto_collapse.tip": "Μετά τη λήξη της σκέψης, η σκέψη αυτόματα συμπιεζεται", "settings.top_p": "Top-P", "settings.top_p.tip": "Η προεπιλογή είναι 1, <PERSON><PERSON><PERSON> μικρ<PERSON><PERSON><PERSON>ρος είναι ο αριθμός, τό<PERSON><PERSON> μικρότερη είναι η ποικιλία του περιεχομένου που παράγεται από το AI και τόσο εύκολοτερο είναι να κατανοείται· όσο μεγαλύτερος είναι, τόσο μεγαλύτερη είναι η ποικιλία των λέξεων που μπορεί να χρησιμοποιήσει το AI.", "suggestions.title": "Προτεινόμενες ερωτήσεις", "thinking": "Σκέψη", "topics.auto_rename": "Δημιουργ<PERSON>α θέματος", "topics.clear.title": "Καθαρισμός μηνυμάτων", "topics.copy.image": "Αντιγραφή ως εικόνα", "topics.copy.md": "Αντιγραφή ως <PERSON>", "topics.copy.plain_text": "Αντιγρα<PERSON><PERSON> ως απλό κείμενο (αφαίρεση Markdown)", "topics.copy.title": "Αντιγραφή", "topics.delete.shortcut": "Πατήστε {{key}} για να διαγράψετε αμέσως", "topics.edit.placeholder": "Εισαγάγετε το νέο όνομα", "topics.edit.title": "Επεξεργα<PERSON><PERSON><PERSON> ονόματος θέματος", "topics.export.image": "Εξαγωγή ως εικόνα", "topics.export.joplin": "Εξαγωγ<PERSON> στο <PERSON>", "topics.export.md": "Εξαγωγή ως <PERSON>", "topics.export.md.reason": "Εξαγωγή σε Markdown (περιλαμβανομένης της σκέψης)", "topics.export.notion": "Εξαγωγή στο Notion", "topics.export.obsidian": "Εξαγωγή στο Obsidian", "topics.export.obsidian_atributes": "Ρυθμίσεις σημείου σημείωσης", "topics.export.obsidian_btn": "ΟΚ", "topics.export.obsidian_created": "Ημερομηνία δημιουργίας", "topics.export.obsidian_created_placeholder": "Επιλέξτε την ημερομηνία δημιουργίας", "topics.export.obsidian_export_failed": "Η εξαγωγή απέτυχε", "topics.export.obsidian_export_success": "Η εξαγωγή ήταν επιτυχής", "topics.export.obsidian_fetch_error": "Αποτυχία λήψης της αποθήκης Obsidian", "topics.export.obsidian_fetch_folders_error": "Αποτυχ<PERSON>α λήψης της δομής φακέλων", "topics.export.obsidian_loading": "Φόρτωση...", "topics.export.obsidian_no_vault_selected": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> επιλέξτε μια αποθήκη πρώτα", "topics.export.obsidian_no_vaults": "Δεν βρέθηκε αποθήκη Obsidian", "topics.export.obsidian_operate": "Επεξεργα<PERSON><PERSON>α μεθόδου", "topics.export.obsidian_operate_append": "Επισυναγωγή", "topics.export.obsidian_operate_new_or_overwrite": "Νέ<PERSON> (επιστροφή σε επιστροφή)", "topics.export.obsidian_operate_placeholder": "Επιλέξτε την μεθόδο επεξεργασίας", "topics.export.obsidian_operate_prepend": "Προσθήκη", "topics.export.obsidian_path": "Διαδρομή", "topics.export.obsidian_path_placeholder": "Επιλέξτε διαδρομή", "topics.export.obsidian_reasoning": "Εξαγωγή αλυσίδας σκέψης", "topics.export.obsidian_root_directory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κατάλογος", "topics.export.obsidian_select_vault_first": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επιλέξτε πρώτα μια αποθήκη", "topics.export.obsidian_source": "Πηγή", "topics.export.obsidian_source_placeholder": "Εισάγετε την πηγή", "topics.export.obsidian_tags": "Ετικέτες", "topics.export.obsidian_tags_placeholder": "Εισάγετε τις ετικέτες, χωρισ<PERSON>ένες με κόμματα στα Αγγλικά, τα ετικέτα μπορεί να μην είναι μόνο αριθμοί", "topics.export.obsidian_title": "Τίτλος", "topics.export.obsidian_title_placeholder": "Εισάγετε τον τίτλο", "topics.export.obsidian_title_required": "Ο τίτλος δεν μπορεί να είναι κενός", "topics.export.obsidian_vault": "Αποθήκη Obsidian", "topics.export.obsidian_vault_placeholder": "Επιλέξτε το όνομα της αποθήκης", "topics.export.siyuan": "Εξαγωγή στο <PERSON>pad", "topics.export.title": "Εξαγωγή", "topics.export.title_naming_failed": "Η δημιουργία του τίτλου απέτυχε, θα χρησιμοποιηθεί ο προεπιλεγμένος τίτλος", "topics.export.title_naming_success": "Ο τίτλος δημιουργήθηκε επιτυχώς", "topics.export.wait_for_title_naming": "Γενικευμένος τίτλος...", "topics.export.word": "Εξαγωγή ως Word", "topics.export.yuque": "Εξαγωγή στο Yuque", "topics.list": "Λίστα θεμάτων", "topics.move_to": "Μετακίνηση στο", "topics.new": "Ξεκινήστε νέα συζήτηση", "topics.pinned": "Σταθερά θέματα", "topics.prompt": "Προ<PERSON><PERSON><PERSON>κώμενα όρια", "topics.prompt.edit.title": "Επεξεργασία προσδοκώμενων όριων", "topics.prompt.tips": "Προσδοκώμενα όρια: προσθέτει επιπλέον επιστημονικές προσθήκες για το παρόν θέμα", "topics.title": "Θέματα", "topics.unpinned": "Αποστέλλω", "translate": "Μετάφραση"}, "code_block": {"collapse": "συμπεριληφθείς", "copy": "Αντιγραφή", "copy.failed": "Η αντιγραφή απέτυχε", "copy.source": "Αντιγρα<PERSON><PERSON> πηγαίου κώδικα", "copy.success": "Επιτυχ<PERSON>ς αντιγραφή", "download": "Λή<PERSON>η", "download.failed.network": "Η λήψη απέτυχε, ελέγξτε τη σύνδεση δικτύου", "download.png": "Λήψη PNG", "download.source": "<PERSON>ήψη πηγαίου κώδικα", "download.svg": "Λήψη SVG", "edit": "Επεξεργασία", "edit.save": "Αποθήκευση αλλαγών", "edit.save.failed": "Η αποθήκευση απέτυχε", "edit.save.failed.message_not_found": "Η αποθήκευση απέτυχε, δεν βρέθηκε το αντίστοιχο μήνυμα", "edit.save.success": "Αποθηκεύτηκε", "expand": "επιλογή", "more": "Περισσότερα", "preview": "Προεπισκόπηση", "preview.copy.image": "Αντιγραφή ως εικόνα", "preview.source": "Προβολή πηγαίου κώδικα", "preview.zoom_in": "Μεγέθυνση", "preview.zoom_out": "Σμίκρυνση", "run": "Εκτέλεση κώδικα", "split": "Διαχω<PERSON>ισμ<PERSON>ς προβολής", "split.restore": "Ακύρωση διαχωρισμού προβολής", "wrap.off": "Απενεργοποίηση αναδίπλωσης", "wrap.on": "Ενεργοποίηση αναδίπλωσης"}, "common": {"add": "Προσθέστε", "advanced_settings": "Προχωρημένες ρυθμίσεις", "and": "και", "assistant": "Εξυπνιασμένη Ενότητα", "avatar": "Εικονίδιο", "back": "Πίσω", "browse": "Περιήγηση", "cancel": "Άκυρο", "chat": "Συζήτηση", "clear": "Καθαρισμός", "close": "Κλείσιμο", "collapse": "Σύμπτυξη", "confirm": "Επιβεβαίωση", "copied": "Αντιγράφηκε", "copy": "Αντιγραφή", "copy_failed": "Αποτυχία αντιγραφής", "cut": "Κοπή", "default": "Προεπιλογή", "delete": "Διαγραφή", "delete_confirm": "Είστε βέβαιοι ότι θέλετε να διαγράψετε;", "description": "Περιγραφή", "disabled": "Απενεργοποιημένο", "docs": "Έγγραφα", "download": "Λή<PERSON>η", "duplicate": "Αντιγραφή", "edit": "Επεξεργασία", "enabled": "Ενεργοποιημένο", "expand": "Επεκτάση", "footnote": "Παραπομπή", "footnotes": "Παραπομπ<PERSON>ς", "fullscreen": "Εισήχθη σε πλήρη οθόνη, πατήστε F11 για να έξω", "i_know": "Το έχω καταλάβει", "inspect": "Επιθεώρηση", "knowledge_base": "Βάση Γνώσεων", "language": "Γλώσσα", "loading": "Φόρτωση...", "model": "<PERSON>ο<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": "Μοντ<PERSON>λα", "more": "Περισσότερα", "name": "Όνομα", "no_results": "Δεν βρέθηκαν αποτελέσματα", "open": "Άνοιγμα", "paste": "Επικόλληση", "prompt": "Ενδεικτικός ρήματος", "provider": "Παρέχων", "reasoning_content": "Έχει σκεφτεί πολύ καλά", "refresh": "Ανανέωση", "regenerate": "Ξαναπαραγωγή", "rename": "Μετονομασία", "reset": "Επαναφορά", "save": "Αποθήκευση", "search": "Αναζήτηση", "select": "Επιλογή", "selectedItems": "Επιλέχθηκαν {{count}} αντικείμενα", "selectedMessages": "Επιλέχθηκαν {{count}} μηνύματα", "settings": "Ρυθμίσεις", "sort": {"pinyin": "Ταξινόμηση κατά Πινγίν", "pinyin.asc": "Αύξουσα ταξινόμηση κατά Πινγίν", "pinyin.desc": "Φθίνουσα ταξινόμηση κατά Πινγίν"}, "success": "Επιτυχία", "swap": "Εναλλαγή", "topics": "Θέματα", "warning": "Προσοχή", "you": "Εσ<PERSON><PERSON>ς"}, "docs": {"title": "Βοήθεια"}, "endpoint_type": {"anthropic": "Anthropic", "gemini": "Gemini", "image-generation": "Δημιουργία Εικόνας", "jina-rerank": "Επαναταξινόμηση Jina", "openai": "OpenAI", "openai-response": "Απάντηση OpenAI"}, "error": {"backup.file_format": "<PERSON><PERSON><PERSON><PERSON> μορφή αρχείου που επιστρέφεται", "chat.response": "Σφάλμα. <PERSON><PERSON><PERSON> δεν έχετε ρυθμίσει το κλειδί API, πηγαίνετε στο ρυθμισμένα > παρέχοντας το πρόσωπο του μοντέλου", "http": {"400": "Σφάλμα ζητήματος, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε αν τα παράμετρα του ζητήματος είναι σωστά. Εάν έχετε αλλάξει τις ρυθμίσεις του μοντέλου, επαναφέρετε τις προεπιλεγμένες ρυθμίσεις.", "401": "Αποτυχία επιβεβαίωσης ταυτότητας, παρα<PERSON><PERSON><PERSON><PERSON> ελέγξτε αν η κλειδί API είναι σωστή", "403": "Απαγορ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> η πρόσβαση, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> μεταφράστε το συγκεκριμένο σφάλμα για να δείτε την αιτία ή επικοινωνήστε με τον παροχεύτη για να μάθετε την αιτία της απαγόρευσης", "404": "Το μοντέλο δεν υπάρχει ή η διαδρομή παραγγελίας είναι λάθος", "429": "Υπερβολική συχνότητα ζητημάτων, παρα<PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "500": "Εσωτερι<PERSON><PERSON> σφάλμα διαχειριστή, παρακαλ<PERSON> δοκιμάστε ξανά", "502": "Σφάλμ<PERSON> φάρων, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "503": "Η υπηρεσία δεν είναι διαθέσιμη, παρα<PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "504": "Υπερχρονισμός φάρων, παρα<PERSON><PERSON>λ<PERSON> δοκιμάστε ξανά"}, "missing_user_message": "Αδυναμ<PERSON>α εναλλαγής απάντησης μοντέλου: το αρχικό μήνυμα χρήστη έχει διαγραφεί. Παρακαλούμε στείλτε ένα νέο μήνυμα για να λάβετε απάντηση από αυτό το μοντέλο", "model.exists": "Το μοντέλο υπ<PERSON><PERSON><PERSON><PERSON>ι ήδη", "no_api_key": "Δεν έχετε ρυθμίσει το κλειδί API", "pause_placeholder": "Διακόπηκε", "provider_disabled": "Ο παρεχόμενος παροχός του μοντέλου δεν είναι ενεργοποιημένος", "render": {"description": "Απέτυχε η ώθηση της εξίσωσης, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε το σωστό μορφάτι της", "title": "Σφάλμα Παρασκήνιου"}, "unknown": "Άγνωστο σφάλμα", "user_message_not_found": "Αδυναμ<PERSON>α εύρεσης της αρχικής μηνύματος χρήστη"}, "export": {"assistant": "βοηθός", "attached_files": "συνημμένα αρχεία", "conversation_details": "λεπτομέρειες συζήτησης", "conversation_history": "Ιστορικ<PERSON> Συζητήσεων", "created": "Ημερομηνία Δημιουργίας", "last_updated": "Τελευτα<PERSON>α ενημέρωση", "messages": "Αριθμός Μηνυμάτων", "user": "<PERSON>ρή<PERSON><PERSON><PERSON>ς"}, "files": {"actions": "Ενέργειες", "all": "Όλα τα αρχεία", "count": "Αριθμός αρχείων", "created_at": "Ημερομηνία δημιουργίας", "delete": "Διαγραφή", "delete.content": "Η διαγραφή του αρχείου θα διαγράψει την αναφορά του σε όλα τα μηνύματα. Είστε σίγουροι ότι θέλετε να διαγράψετε αυτό το αρχείο;", "delete.paintings.warning": "Το σχεδίο περιλαμβάνει αυτή την εικόνα και δεν είναι παρόλως δυνατή η διαγραφή.", "delete.title": "Διαγρα<PERSON>ή αρχείου", "document": "Έγγραφο", "edit": "Επεξεργασία", "file": "Αρχείο", "image": "Εικόνα", "name": "Όνομα αρχείου", "open": "Άνοιγμα", "size": "Μέγεθος", "text": "Κείμενο", "title": "Αρχεία", "type": "Τύπος"}, "gpustack": {"keep_alive_time.description": "<PERSON>ρ<PERSON><PERSON><PERSON> που ο μοντ<PERSON><PERSON>ος παραμένει στη μνήμη (προεπιλογή: 5 λεπτά)", "keep_alive_time.placeholder": "λεπτά", "keep_alive_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> διατήρη<PERSON>ης ενεργοποίησης", "title": "GPUStack"}, "history": {"continue_chat": "Συνεχίστε το συνομιλημένο", "locate.message": "Εφαρμογή στο μήνυμα", "search.messages": "Αναζήτη<PERSON>η όλων των μηνυμάτων", "search.placeholder": "Αναζήτηση θεμάτων ή μηνυμάτων...", "search.topics.empty": "Δεν βρέθηκαν σχετικά θέματα, πατήστε Enter για να αναζητήσετε όλα τα μηνύματα", "title": "Αναζήτηση θεμάτων"}, "html_artifacts": {"code": "Κώδικας", "generating": "Δημιουργία", "preview": "Προεπισκόπηση", "split": "Διαχωρισμ<PERSON>ς"}, "knowledge": {"add": {"title": "Προσθήκη βιβλιοθήκης γνώσεων"}, "add_directory": "Προσθήκη καταλόγου", "add_file": "Προσθήκη αρχείου", "add_note": "Προσθήκη σημειώματος", "add_sitemap": "Χ<PERSON>ρτης τόπων", "add_url": "Προσθήκη διευθύνσεως", "cancel_index": "Άκυρη ευρετήριοποίηση", "chunk_overlap": "Μέγ<PERSON><PERSON>ος επιφάνειας", "chunk_overlap_placeholder": "Προεπιλογή (δεν συνιστάται να το αλλάξετε)", "chunk_overlap_tooltip": "Το ποσοστό επιφάνειας επιφάνειας μεταξύ γειτνιώντων κειμένων μπλοκ, για να εξασφαλίσετε ότι τα κείμενα μπλοκ μετά τη διακοσμηση εξακολουθούν να έχουν σχέση σε προσδιορισμό, βελτιώνοντας την συνολική αποτελεσματικότητα επεξεργασίας με μοντέλα μεγάλου κειμένου", "chunk_size": "Μέγ<PERSON><PERSON>ος μερισμού", "chunk_size_change_warning": "Η αλλαγή του μεγέθους μερισμού και της επιφάνειας επιφάνειας εφαρμόζεται μόνο για νέα προσθέτομεν αρχεία", "chunk_size_placeholder": "Προεπιλογή (δεν συνιστάται να το αλλάξετε)", "chunk_size_too_large": "Το μέγεθος μερισμού δεν μπορεί να ξεπεράσει το όριο πλάτους επιρροής του μοντέλου ({{max_context}})", "chunk_size_tooltip": "Διαχωρισμός των έγγραφων σε μεριδισμούς, με το μέγεθος κάθε μεριδισμού να μην ξεπεράζει το όριο πλάτους επιρροής του μοντέλου", "clear_selection": "Καθαρισ<PERSON><PERSON>ς επιλογής", "delete": "Διαγραφή", "delete_confirm": "Είστε σίγου<PERSON>ος ότι θέλετε να διαγράψετε αυτή τη βάση γνώσεων;", "dimensions": "Διαστ<PERSON><PERSON><PERSON><PERSON>ς ενσωμάτωσης", "dimensions_auto_set": "Αυτόματη ρύθμιση διαστάσεων ενσωμάτωσης", "dimensions_default": "Το μοντέλο θα χρησιμοποιήσει τις προεπιλεγμένες διαστάσεις ενσωμάτωσης", "dimensions_error_invalid": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε μέγεθος διαστάσεων ενσωμάτωσης", "dimensions_set_right": "⚠️ Βεβαιωθείτε ότι το μοντέλο υποστηρίζει το καθορισμένο μέγεθος διαστάσεων ενσωμάτωσης", "dimensions_size_placeholder": " Μέγεθος διαστά<PERSON>εων ενσωμάτωσης, π.χ. 1024", "dimensions_size_too_large": "Οι διαστάσεις ενσωμάτωσης δεν μπορούν να υπερβούν το όριο περιεχομένου του μοντέλου ({{max_context}})", "dimensions_size_tooltip": "Το μέγεθος των διαστάσεων ενσωμάτωσης. Όσο μεγαλύτερη η τιμή, τόσο περισσότερες οι διαστάσεις ενσωμάτωσης, α<PERSON><PERSON><PERSON> και οι απαιτούμενες μονάδες (<PERSON><PERSON><PERSON>).", "directories": "Κατάλογοι", "directory_placeholder": "Εισάγετε το δρομολόγιο του καταλόγου", "document_count": "Ποσότητα κειμένων που ζητούνται", "document_count_default": "Προεπιλογή", "document_count_help": "Όσο μεγαλύτερη είναι η ποσότητα των κειμένων που ζητούνται, τόσο περισσότερες πληροφορίες παρέχονται, αλ<PERSON><PERSON> και οι καταναλωτικ<PERSON><PERSON> Token επειδή περισσότερα", "drag_file": "Βάλτε το αρχείο εδώ", "edit_remark": "Μεταβολή σημειώματος", "edit_remark_placeholder": "Εισάγετε το σημείωμα", "embedding_model_required": "Το μοντέλο ενσωμάτωσης της βάσης γνώσης είναι υποχρεωτικό", "empty": "Λεηλα<PERSON><PERSON><PERSON> βάσης γνώσεων", "file_hint": "Υποστηρίζεται το {{file_types}} μορφάττων", "index_all": "Ευρετήριοποίηση όλων", "index_cancelled": "Η ευρετήριοποίηση διακόπηκε", "index_started": "Η ευρετήριοποίηση ξεκίνησε", "invalid_url": "Μη έγκυρη διευθύνση", "model_info": "Πληροφορ<PERSON>ες μοντέλου", "name_required": "Το όνομα της βάσης γνώσης είναι υποχρεωτικό", "no_bases": "Λεηλα<PERSON><PERSON><PERSON> βάσης γνώσεων", "no_match": "Δεν βρέθηκαν στοιχεία γνώσεων", "no_provider": "Η παροχή υπηρεσιών μοντέλου βάσης γνώσεων χαθηκε, αυτή η βάση γνώσεων δεν θα υποστηρίζεται πλέον, παρα<PERSON><PERSON><PERSON>είστε να δημιουργήσετε ξανά μια βάση γνώσεων", "not_set": "Δεν έχει ρυθμιστεί", "not_support": "Το μοντέλ<PERSON> βάσης γνώσεων έχει ενημερωθεί, αυτή η βάση γνώσεων δεν θα υποστηρίζεται πλέον, παρακα<PERSON>είστε να δημιουργήσετε ξανά μια βάση γνώσεων", "notes": "Σημειώματα", "notes_placeholder": "Εισάγετε πρόσθετες πληροφορίες ή πληροφορίες προσδιορισμού για αυτή τη βάση γνώσεων...", "quota": "Διαθέσιμο όριο για {{name}}: {{quota}}", "quota_infinity": "Διαθέσιμο όριο για {{name}}: Απεριόριστο", "rename": "Μετονομασία", "search": "Αναζήτηση βάσης γνώσεων", "search_placeholder": "Εισάγετε την αναζήτηση", "settings": {"preprocessing": "Προεπεξεργασία", "preprocessing_tooltip": "Προεπεξεργασία των ανεβασμένων αρχείων με χρήση OCR", "title": "Ρυθμίσεις Γνώσης"}, "sitemap_placeholder": "Εισάγετε τη διεύθυνση URL του χάρτη τόπων", "sitemaps": "Στοιχεία του δικτύου", "source": "Πηγή", "status": "Κατάσταση", "status_completed": "Ολοκληρώθηκε", "status_embedding_completed": "Η ενσωμάτωση ολοκληρώθηκε", "status_embedding_failed": "Η ενσωμάτωση απέτυχε", "status_failed": "Αποτυχία", "status_new": "Προστέθηκε", "status_pending": "Εκκρεμής", "status_preprocess_completed": "Η προεπεξεργασία ολοκληρώθηκε", "status_preprocess_failed": "Η προεπεξεργασία απέτυχε", "status_processing": "Επεξεργασία", "threshold": "Περιθώριο συνάφειας", "threshold_placeholder": "Δεν έχει ρυθμιστεί", "threshold_too_large_or_small": "Το περιθώριο δεν μπορεί να είναι μεγαλύτερο από 1 ή μικρότερο από 0", "threshold_tooltip": "Χρησιμοποιείται για τη μετρηση της σχέσης συνάφειας μεταξύ της ερώτησης του χρήστη και των περιεχομένων της βάσης γνώσεων (0-1)", "title": "Βάση γνώσεων", "topN": "Ποσότητα αποτελεσμάτων που επιστρέφονται", "topN_placeholder": "Δεν έχει ρυθμιστεί", "topN_too_large_or_small": "Ο αριθμός των αποτελεσμάτων δεν μπορεί να είναι μεγαλύτερος από 30 ή μικρότερος από 1", "topN_tooltip": "Η ποσότητα των επιστρεφόμενων αποτελεσμάτων που συνάφονται, όσ<PERSON> μεγαλύτερη είναι η τιμή, τόσο περισσότερα αποτελέσματα συνδέονται, αλλά και οι καταναλωτικοί Token επειδή περισσότερα", "url_added": "Η διεύθυνση προστέθηκε", "url_placeholder": "Εισάγετε τη διεύθυνση, χωρ<PERSON>στε πολλαπλές διευθύνσεις με επιστροφή", "urls": "Διευθύνσεις"}, "languages": {"arabic": "Αραβικά", "chinese": "Σίναρα Κινέζικά", "chinese-traditional": "Παραδοσιακά Κινέζικά", "english": "Αγγλικ<PERSON>", "french": "Γαλλικά", "german": "Γερμανικά", "indonesian": "Ινδονησιακά", "italian": "Ιταλικά", "japanese": "Ιαπωνικά", "korean": "Κορεά<PERSON>ικά", "malay": "Μαλαισιακά", "polish": "Πολωνικά", "portuguese": "Πορτογαλικά", "russian": "Ρωσικά", "spanish": "Ισπανικά", "thai": "Ταϊλανδικά", "turkish": "Τουρκικά", "urdu": "Ουρντού", "vietnamese": "Βιετναμέζικα"}, "launchpad": {"apps": "Εφαρμογές", "minapps": "Μικρ<PERSON>ς εφαρμογές"}, "lmstudio": {"keep_alive_time.description": "<PERSON>ρ<PERSON><PERSON><PERSON> που ο μοντέ<PERSON>ος διατηρείται στη μνήμη μετά από το συνομιλητή (προεπιλογή: 5 λεπτά)", "keep_alive_time.placeholder": "λεπτά", "keep_alive_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> διατήρη<PERSON>ης ενεργοποίησης", "title": "LM Studio"}, "memory": {"actions": "Ενέργειες", "add_failed": "Αποτυχία προσθήκης μνήμης", "add_first_memory": "Προσθέστε την πρώτη σας μνήμη", "add_memory": "Προσθήκη μνήμης", "add_new_user": "Προσθήκη νέου χρήστη", "add_success": "Η μνήμη προστέθηκε επιτυχώς", "add_user": "Προσθήκη χρήστη", "add_user_failed": "Αποτυχία προσθήκης χρήστη", "all_users": "Όλοι οι χρήστες", "cannot_delete_default_user": "Δεν είναι δυνατή η διαγραφή του προεπιλεγμένου χρήστη", "configure_memory_first": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> ρυθμίστε πρώτα τις ρυθμίσεις μνήμης", "content": "Περιεχόμενο", "current_user": "Τρέχων χρήστης", "custom": "Προσαρμοσμένο", "default": "Προεπιλογή", "default_user": "Προεπιλεγ<PERSON><PERSON><PERSON>ος χρήστης", "delete_confirm": "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτή τη μνήμη;", "delete_confirm_content": "Είστε βέβαιοι ότι θέλετε να διαγράψετε {{count}} μνήμες;", "delete_confirm_single": "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτή τη μνήμη;", "delete_confirm_title": "Διαγραφή μνήμης", "delete_failed": "Αποτυχία διαγραφής μνήμης", "delete_selected": "Διαγραφή επιλεγμένων", "delete_success": "Η μνήμη διαγράφηκε επιτυχώς", "delete_user": "Διαγρα<PERSON><PERSON> χρήστη", "delete_user_confirm_content": "Είστε βέβαιοι ότι θέλετε να διαγράψετε τον χρήστη {{user}} και όλες τις μνήμες του;", "delete_user_confirm_title": "Διαγρα<PERSON><PERSON> χρήστη", "delete_user_failed": "Αποτυχία διαγραφής χρήστη", "description": "Η λειτουργία μνήμης σας επιτρέπει να αποθηκεύετε και να διαχειρίζεστε πληροφορίες από την αλληλεπίδρα<PERSON>ή σας με τον βοηθό. Μπορείτε να προσθέτετε, επεξ<PERSON>ργάζεστε και διαγράφετε μνήμες, καθώς και να τις φιλτράρετε και να τις αναζητάτε.", "edit_memory": "Επεξεργασία μνήμης", "embedding_dimensions": "Διαστ<PERSON><PERSON><PERSON><PERSON>ς ενσωμάτωσης", "embedding_model": "Μοντ<PERSON><PERSON><PERSON> ενσωμάτωσης", "enable_global_memory_first": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ενεργοποιήστε πρώτα τη γενική μνήμη", "end_date": "Ημερομηνία λήξης", "global_memory": "Γενική μνήμη", "global_memory_description": "Απαιτεί<PERSON><PERSON><PERSON> η ενεργοποίηση της γενικής μνήμης στις ρυθμίσεις του βοηθού για να χρησιμοποιηθεί", "global_memory_disabled_desc": "Για να χρησιμοποιήσετε τη λειτουργία μνήμης, ενεργοποιήστε πρώτα τη γενική μνήμη στις ρυθμίσεις του βοηθού.", "global_memory_disabled_title": "Η γενική μνήμη είναι απενεργοποιημένη", "global_memory_enabled": "Η γενική μνήμη είναι ενεργοποιημένη", "go_to_memory_page": "Μετάβαση στη σελίδα μνήμης", "initial_memory_content": "Καλώς ήρθατε! Αυτή είναι η πρώτη σας μνήμη.", "llm_model": "Μοντέ<PERSON>ο LLM", "load_failed": "Αποτυχία φόρτωσης μνήμης", "loading": "Φόρτωση μνήμης...", "loading_memories": "Φόρτωση μνήμης...", "memories_description": "Εμφάνιση {{count}} / {{total}} μνήμης", "memories_reset_success": "Όλες οι μνήμες του {{user}} επαναφ<PERSON>ρθηκαν επιτυχώς", "memory": "μνήμη", "memory_content": "Περιεχόμενο μνήμης", "memory_placeholder": "Εισαγωγή περιεχομένου μνήμης...", "new_user_id": "Νέο ID χρήστη", "new_user_id_placeholder": "Εισαγωγή μοναδικού ID χρήστη", "no_matching_memories": "Δεν βρέθηκαν αντίστοιχες μνήμες", "no_memories": "Δεν υπάρχουν μνήμες", "no_memories_description": "Ξεκινήστε προσθέτοντας την πρώτη σας μνήμη", "not_configured_desc": "Παρακ<PERSON><PERSON><PERSON> ρυθμίστε τα μοντέλα ενσωμάτωσης και LLM στις ρυθμίσεις μνήμης για να ενεργοποιήσετε τη λειτουργία μνήμης.", "not_configured_title": "Η μνήμη δεν έχει ρυθμιστεί", "pagination_total": "{{start}}-{{end}} από {{total}} συνολικά", "please_enter_memory": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε περιεχόμενο μνήμης", "please_select_embedding_model": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε μοντέλο ενσωμάτωσης", "please_select_llm_model": "Παρακ<PERSON><PERSON><PERSON> επιλέξτε μοντέλο LLM", "reset_filters": "Επαναφορ<PERSON> φίλτρων", "reset_memories": "Επαναφορ<PERSON> μνήμης", "reset_memories_confirm_content": "Είστε βέβαιοι ότι θέλετε να διαγράψετε μόνιμα όλες τις μνήμες του {{user}}; Η ενέργεια αυτή δεν μπορεί να αναιρεθεί.", "reset_memories_confirm_title": "Επανα<PERSON><PERSON><PERSON><PERSON>λων των μνήμων", "reset_memories_failed": "Αποτυχία επαναφορ<PERSON>ς μνήμης", "reset_user_memories": "Επανα<PERSON>ο<PERSON><PERSON> μνήμης χρήστη", "reset_user_memories_confirm_content": "Είστε βέβαιοι ότι θέλετε να επαναφέρετε όλες τις μνήμες του {{user}};", "reset_user_memories_confirm_title": "Επανα<PERSON>ο<PERSON><PERSON> μνήμης χρήστη", "reset_user_memories_failed": "Αποτυχ<PERSON>α επαναφο<PERSON><PERSON>ς μνήμης χρήστη", "score": "Βαθμολογία", "search": "Αναζήτηση", "search_placeholder": "Αναζήτηση μνήμης...", "select_embedding_model_placeholder": "Επιλέξτε μοντέλο ενσωμάτωσης", "select_llm_model_placeholder": "Επιλέξτε μοντέλο LLM", "select_user": "Επιλογή χρήστη", "settings": "Ρυθμίσεις", "settings_title": "Ρυθμίσεις μνήμης", "start_date": "Ημερομηνία έναρξης", "statistics": "Στατιστικά", "stored_memories": "Αποθηκευμένες μνήμες", "switch_user": "Αλλαγή χρήστη", "switch_user_confirm": "Αλλαγή περιβάλλοντος χρήστη στο {{user}};", "time": "Ώρα", "title": "Γενική μνήμη", "total_memories": "μνήμες", "try_different_filters": "Δοκιμάστε να αλλάξετε τα κριτήρια αναζήτησης", "update_failed": "Αποτυχία ενημέρωσης μνήμης", "update_success": "Η μνήμη ενημερώθηκε επιτυχώς", "user": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "user_created": "Ο χρήστης {{user}} δημιουργήθηκε και η αλλαγή ήταν επιτυχής", "user_deleted": "Ο χρήστης {{user}} διαγράφηκε επιτυχώς", "user_id": "ID χρήστη", "user_id_exists": "Το ID χρήστη υπάρχει ήδη", "user_id_invalid_chars": "Το ID χρήστη μπορεί να περιέχει μόνο γράμματα, αριθμούς, παύλες και κάτω παύλες", "user_id_placeholder": "Εισαγω<PERSON><PERSON> ID χρήστη (προαιρετικό)", "user_id_required": "Το ID χρήστη είναι υποχρεωτικό", "user_id_reserved": "Το 'default-user' εί<PERSON><PERSON><PERSON> δεσμευμένο, χρησιμοποιήστε άλλο ID", "user_id_rules": "Το ID χρήστη πρέπει να είναι μονα<PERSON>ικ<PERSON> και να περιέχει μόνο γράμματα, αριθμούς, παύλες (-) και κάτω παύλες (_)", "user_id_too_long": "Το ID χρήστη δεν μπορεί να ξεπερνά τους 50 χαρακτήρες", "user_management": "Διαχείριση χρηστών", "user_memories_reset": "Όλες οι μνήμες του {{user}} επαναφέρθηκαν", "user_switch_failed": "Αποτυχία αλλαγής χρήστη", "user_switched": "Το περιβάλλον χρήστη άλλαξε στο {{user}}", "users": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "message": {"agents": {"import.error": "Η εισαγωγή απέτυχε", "imported": "Εισήχθη επιτυχώς"}, "api.check.model.title": "Επιλέξτε το μοντέλο που θα ελέγξετε", "api.connection.failed": "Η σύνδεση απέτυχε", "api.connection.success": "Η σύνδεση ήταν επιτυχής", "assistant.added.content": "Ο ενεργοπο<PERSON>η<PERSON><PERSON><PERSON>ος αστρόναυτης προστέθηκε επιτυχώς", "attachments": {"pasted_image": "Εικόνα στο πινάκιδα", "pasted_text": "Κείμενο στο πινάκιδα"}, "backup.failed": "Η αντιγραφή ασφαλείας απέτυχε", "backup.start.success": "Η αρχή της αντιγραφής ασφαλε<PERSON>ας ήταν επιτυχής", "backup.success": "Η αντιγραφή ασφαλε<PERSON>ας ήταν επιτυχής", "chat.completion.paused": "Η συζήτηση διακόπηκε", "citation": "{{count}} αναφορές", "citations": "Περιεχόμενα αναφοράς", "copied": "Αντιγράφηκε", "copy.failed": "Η αντιγραφή απέτυχε", "copy.success": "Η αντιγραφή ήταν επιτυχής", "delete.confirm.content": "Επιβεβαιώνετε τη διαγραφή των {{count}} επιλεγμένων μηνυμάτων;", "delete.confirm.title": "Επιβεβαίωση Διαγραφής", "delete.failed": "Η διαγραφή απέτυχε", "delete.success": "Η διαγραφή ήταν επιτυχής", "download.failed": "Αποτυχία λήψης", "download.success": "Λήψη ολοκληρώθηκε", "empty_url": "Αδυναμ<PERSON><PERSON> λήψης της εικόνας, πιθ<PERSON><PERSON><PERSON><PERSON> οι οδηγίες να περιέχουν ευαίσθητο περιεχόμενο ή απαγορευμένες λέξεις", "error.chunk_overlap_too_large": "Η επικάλυψη μεριδίων δεν μπορεί να είναι μεγαλύτερη από το μέγεθος του μεριδίου", "error.dimension_too_large": "Το μέγεθος του περιεχομένου είναι πολύ μεγάλο", "error.enter.api.host": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε τη διεύθυνση API σας", "error.enter.api.key": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το κλειδί API σας", "error.enter.model": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε ένα μοντέλο", "error.enter.name": "Π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε ένα όνομα για τη βάση γνώσεων", "error.fetchTopicName": "Αποτυχία ονομασίας θέματος", "error.get_embedding_dimensions": "Απέτυχε η πρόσληψη διαστάσεων ενσωμάτωσης", "error.invalid.api.host": "Μη έγκυρη διεύθυνση API", "error.invalid.api.key": "Μη έγκυρο κλειδί API", "error.invalid.enter.model": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε ένα μοντέλο", "error.invalid.nutstore": "Μη έγκυρη ρύθμιση Nutstore", "error.invalid.nutstore_token": "Μη έγκυρο Token Nutstore", "error.invalid.proxy.url": "Μη έγκυρη διεύθυνση προξενικού", "error.invalid.webdav": "Μη έγκυρη ρύθμιση WebDAV", "error.joplin.export": "Η εξαγωγή του <PERSON><PERSON><PERSON> απέτυχε, παρα<PERSON><PERSON><PERSON><PERSON>ίστε να ελέγξετε τη σύνδεση και τη διαμόρφωση κατά τη διατύπωση του χειρισμού", "error.joplin.no_config": "Δεν έχετε διαθέσιμο το Token εξουσιοδότησης του <PERSON><PERSON><PERSON> ή το URL του <PERSON><PERSON><PERSON>", "error.markdown.export.preconf": "Η εξαγωγή αρχείου Markdown στο προϋπολογισμένο μοντέλο απέτυχε", "error.markdown.export.specified": "Η εξαγωγή αρχείου Markdown απέτυχε", "error.notion.export": "Σφάλμα στην εξαγωγή του Notion, παρακαλείστε να ελέγξετε τη σύνδεση και τη διαμόρφωση κατά τη διατύπωση του χειρισμού", "error.notion.no_api_key": "Δεν έχετε διαθέσιμο το API Key του Notion ή το ID της βάσης του Notion", "error.siyuan.export": "Η έκθεση σημειώσεων <PERSON> απέτυχε, ελέγξτε την κατάσταση σύνδεσης και τις ρυθμίσεις σύμφωνα με τα έγγραφα", "error.siyuan.no_config": "Δεν έχει ρυθμιστεί η διεύθυνση API ή το Token του Siyuan Notes", "error.yuque.export": "Σφάλμα στην εξαγωγή της <PERSON>, παρακαλείστε να ελέγξετε τη σύνδεση και τη διαμόρφωση κατά τη διατύπωση του χειρισμού", "error.yuque.no_config": "Δεν έχετε διαθέσιμο το Token της Yu<PERSON> ή το URL της βάσης της <PERSON>que", "group.delete.content": "Η διαγραφή της ομάδας θα διαγράψει τις ερωτήσεις των χρηστών και όλες τις απαντήσεις του αστρόναυτη", "group.delete.title": "Διαγρα<PERSON><PERSON> ομάδας", "ignore.knowledge.base": "Λειτουργ<PERSON><PERSON> σύνδεσης ενεργοποιημένη, αγνοείται η βάση γνώσεων", "loading.notion.exporting_progress": "Εξάγεται στο Notion ({{current}}/{{total}})...", "loading.notion.preparing": "Ετοιμάζεται η εξαγωγή στο Notion...", "mention.title": "Εναλλαγ<PERSON> απάντη<PERSON>ης αστρόναυτη", "message.code_style": "Στυλ κώδικα", "message.delete.content": "Θέλετε να διαγράψετε αυτό το μήνυμα;", "message.delete.title": "Διαγρα<PERSON><PERSON> μηνύματος", "message.multi_model_style": "Στυλ πολλα<PERSON>λών απαντή<PERSON>εων μοντέλου", "message.multi_model_style.fold": "Κατάσταση ενσωμάτωσης", "message.multi_model_style.fold.compress": "Εναλλαγή στη συμπιεσμένη διάταξη", "message.multi_model_style.fold.expand": "Εναλλαγή στην επεκτατική διάταξη", "message.multi_model_style.grid": "Διάταξη κάρτας", "message.multi_model_style.horizontal": "Διάταξη επίπεδης", "message.multi_model_style.vertical": "Διάταξη κάθετης", "message.style": "Στυλ μηνύματος", "message.style.bubble": "Αερογεύματα", "message.style.plain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "processing": "Επεξεργασία...", "regenerate.confirm": "Η επαναδημιουργία θα αφαιρέσει το τρέχον μήνυμα", "reset.confirm.content": "Θέλετε να επαναφέρετε όλα τα δεδομένα;", "reset.double.confirm.content": "Όλα τα δεδομένα σας θα χαθούν, ε<PERSON>ν δεν έχετε κάνει αντιγραφή, δεν θα μπορείτε να ανακτήσετε τα δεδομένα, είστε σίγουροι ότι θέλετε να συνεχίσετε;", "reset.double.confirm.title": "Η απώλεια δεδομένων!!!", "restore.failed": "Η αποκατάσταση απέτυχε", "restore.success": "Η αποκατάστα<PERSON>η ήταν επιτυχής", "save.success.title": "Η αποθήκευση ήταν επιτυχής", "searching": "Ενεργοποιείται αναζήτηση στο διαδίκτυο...", "success.joplin.export": "Η εξαγωγή στο <PERSON>αν επιτυχής", "success.markdown.export.preconf": "Η εξαγωγή αρχείου Markdown στο προϋπολογισμένο μοντέλο ήταν επιτυχής", "success.markdown.export.specified": "Η εξαγωγή αρχείου Markdown ήταν επιτυχής", "success.notion.export": "Η εξαγωγή στο Notion ήταν επιτυχής", "success.siyuan.export": "Επιτυχής εξαγωγή στις σημειώσεις <PERSON>", "success.yuque.export": "Η εξαγωγή στη Yuque ήταν επιτυχής", "switch.disabled": "Παρακ<PERSON><PERSON><PERSON>ίστε να περιμένετε τη λήξη της τρέχουσας απάντησης", "tools": {"abort_failed": "Αποτυχία διακοπής κλήσης εργαλείου", "aborted": "Η κλήση του εργαλείου διακόπηκε", "autoApproveEnabled": "Αυτό το εργα<PERSON><PERSON><PERSON><PERSON> έχει ενεργοποιημένη την αυτόματη έγκριση", "cancelled": "Ακυρώθηκε", "completed": "Ολοκληρώθηκε", "error": "Προέκυψε σφάλμα", "invoking": "κλήση σε εξέλιξη", "pending": "Εκκρεμεί", "preview": "Προεπισκόπηση", "raw": "Ακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>το"}, "topic.added": "Η θεματική προστέθηκε επιτυχώς", "upgrade.success.button": "Επανεκκίνηση", "upgrade.success.content": "Επανεκκίνηση για να ολοκληρώσετε την ενημέρωση", "upgrade.success.title": "Η ενημέρωση ήταν επιτυχής", "warn.notion.exporting": "Εξαγωγή στο <PERSON>, μην επαναλάβετε την διαδικασία εξαγωγής!", "warn.siyuan.exporting": "Γίνεται εξαγωγή στις σημειώσεις <PERSON>· μην ξαναζητήσετε την έκθεση!", "warn.yuque.exporting": "Γίνεται έκθεση Yuque· μην ξαναζητήσετε την έκθεση!", "warning.rate.limit": "Υπερβολική συχνότητα στείλατε παρακ<PERSON>λ<PERSON> περιμένετε {{seconds}} δευτερόλεπτα και προσπαθήστε ξανά", "websearch": {"cutoff": "Περικόπτεται η αναζήτηση...", "fetch_complete": "Ολοκληρώθηκαν {{count}} αναζητήσεις...", "rag": "Εκτελείται RAG...", "rag_complete": "Διατηρούνται {{countAfter}} από τα {{countBefore}} αποτελέσματα...", "rag_failed": "Το RAG απέτυχε, επιστρέφεται κενό αποτέλεσμα..."}}, "minapp": {"add_to_launchpad": "Προσθήκη στο Launchpad", "add_to_sidebar": "Προσθήκη στην πλευρική μπάρα", "popup": {"close": "Κλείσιμο της εφαρμογής", "devtools": "Εργαλεία προγραμματιστή", "goBack": "Πίσω", "goForward": "Μπροστά", "minimize": "Ελαχιστοποίηση της εφαρμογής", "openExternal": "Άνοιγμα στον περιηγητή", "open_link_external_off": "Τρέχον: Άνοιγμα συνδέσμου χρησιμοποιώντας το προεπιλεγμένο παράθυρο", "open_link_external_on": "Τρέχον: Άνοιγμα συνδέσμου στον περιηγητή", "refresh": "Ανανέωση", "rightclick_copyurl": "Αντιγραφή URL με δεξί κλικ"}, "remove_from_launchpad": "Κατάργηση από το Launchpad", "remove_from_sidebar": "Κατάργηση από την πλευρική μπάρα", "sidebar": {"close": {"title": "Κλείσιμο"}, "closeall": {"title": "Κλείσιμο όλων"}, "hide": {"title": "Απόκρυψη"}, "remove_custom": {"title": "Διαγραφή προσαρμοσμένης εφαρμογής"}}, "title": "Μικρόπρογραμμα"}, "miniwindow": {"alert": {"google_login": "Υπόδειξη: Αν συναντήσετε την ειδοποίηση «Μη εμπιστευόμενος περιηγητής» κατά τη σύνδεση στο Google, πρώτα ολοκληρώστε τη σύνδεση του λογαριασμού σας μέσω της εφαρμογής Google στη λίστα μικροεφαρμογών, και στη συνέχεια χρησιμοποιήστε τη σύνδεση Google σε άλλες μικροεφαρμογές"}, "clipboard": {"empty": "Το πινάκιδα κόπων είναι άδειο"}, "feature": {"chat": "Απάντηση σ' αυτή την ερώτηση", "explanation": "Εξήγηση", "summary": "Σύνοψη", "translate": "Μετάφραση κειμένου"}, "footer": {"backspace_clear": "Πατήστε το πλήκτρο Backspace για να κάνετε εκκαθάριση", "copy_last_message": "Παράκαμε το τελευταίο μήνυμα", "esc": "πατήστε ESC για {{action}}", "esc_back": "Επιστροφή", "esc_close": "Κλείσιμο παραθύρου", "esc_pause": "Παύση"}, "input": {"placeholder": {"empty": "Ρώτα τον {{model}} για βοήθεια...", "title": "Τι θέλεις να κάνεις με το κείμενο που είναι παρακάτω"}}, "tooltip": {"pin": "Καρφίτσωμα παραθύρου"}}, "models": {"add_parameter": "Προσθήκη παραμέτρων", "all": "Όλα", "custom_parameters": "Προσαρμοσμένοι παράμετροι", "dimensions": "{{dimensions}} διαστάσεις", "edit": "Επεξεργα<PERSON><PERSON>α μοντέλου", "embedding": "Ενσωμάτωση", "embedding_dimensions": "Διαστ<PERSON><PERSON><PERSON><PERSON>ς ενσωμάτωσης", "embedding_model": "Μοντ<PERSON><PERSON><PERSON> ενσωμάτωσης", "embedding_model_tooltip": "Κάντε κλικ στο κουμπί Διαχείριση στο παράθυρο Ρυθμίσεις -> Υπηρεσία Μοντέλων", "enable_tool_use": "Ενεργοποίηση κλήσης εργαλείου", "function_calling": "Ξεχωριστική Κλήση Συναρτήσεων", "no_matches": "Δεν υπάρχουν διαθέσιμα μοντέλα", "parameter_name": "Όνομα παραμέτρου", "parameter_type": {"boolean": "Πιθανότητα", "json": "JSON", "number": "Αριθμός", "string": "Συμβολοσειρά"}, "pinned": "Κατακερματισμένο", "price": {"cost": "Κόστος", "currency": "Νόμισμα", "custom": "Προσαρμογή", "custom_currency": "Προσαρμοσμένο νόμισμα", "custom_currency_placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε προσαρμοσμένο νόμισμα", "input": "Τιμ<PERSON> εισόδου", "million_tokens": "Ένα εκατομμύριο Token", "output": "Τιμ<PERSON> εξόδου", "price": "Τιμή"}, "reasoning": "Συλλογισμ<PERSON>ς", "rerank_model": "Μοντ<PERSON><PERSON><PERSON> αναδιάταξης", "rerank_model_not_support_provider": "Ο επαναξιολογη<PERSON><PERSON><PERSON><PERSON><PERSON> μοντέλος δεν υποστηρίζει αυτόν τον πάροχο ({{provider}})", "rerank_model_support_provider": "Σημειώστε ότι το μοντέλο αναδιά<PERSON>αξης υποστηρίζεται από μερικούς παρόχους ({{provider}})", "rerank_model_tooltip": "Κάντε κλικ στο κουμπί Διαχείριση στο παράθυρο Ρυθμίσεις -> Υπηρεσία Μοντέλων", "search": "Αναζήτηση μοντέλου...", "stream_output": "Δια<PERSON><PERSON><PERSON><PERSON>", "type": {"embedding": "ενσωμάτωση", "free": "δωρεάν", "function_calling": "κλήση συνάρτησης", "reasoning": "λογική", "rerank": "Τακτοποιώ", "select": "Επιλέξτε τύπο μοντέλου", "text": "κείμενο", "vision": "εικόνα", "websearch": "δικτύωση"}}, "navbar": {"expand": "Επισκευή διαλόγου", "hide_sidebar": "Απόκρυψη πλάγιας μπάρας", "show_sidebar": "Εμφάνιση πλάγιας μπάρας"}, "notification": {"assistant": "Απάντηση Βοηθού", "knowledge.error": "{{error}}", "knowledge.success": "Επιτυχής προσθήκη {{type}} στη βάση γνώσης", "tip": "Εάν η απάντηση είναι επιτυχής, η ειδοποίηση εμφανίζεται μόνο για μηνύματα που υπερβαίνουν τα 30 δευτερόλεπτα"}, "ollama": {"keep_alive_time.description": "<PERSON>ρ<PERSON><PERSON><PERSON> που ο μοντ<PERSON><PERSON>ος διατηρείται στη μνήμη μετά τη συζήτηση (προεπιλογή: 5 λεπτά)", "keep_alive_time.placeholder": "λεπτά", "keep_alive_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> διατήρη<PERSON>ης ενεργοποίησης", "title": "Ollama"}, "paintings": {"aspect_ratio": "<PERSON><PERSON><PERSON><PERSON> διαστάσεων", "aspect_ratios": {"landscape": "Οριζόντια εικόνα", "portrait": "Κάθετη εικόνα", "square": "Τετράγωνο"}, "auto_create_paint": "Αυτόματη δημιουργία εικόνας", "auto_create_paint_tip": "Μετά τη δημιουργία της εικόνα<PERSON>, θα δημιουργηθεί αυτόματα νέα εικόνα", "background": "Φόντο", "background_options": {"auto": "Αυτόματο", "opaque": "Αδιαφανές", "transparent": "Δια<PERSON>ανές"}, "button.delete.image": "Διαγραφ<PERSON> εικόνας", "button.delete.image.confirm": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την εικόνα;", "button.new.image": "Νέα εικόνα", "edit": {"image_file": "Επεξεργασμένη εικόνα", "magic_prompt_option_tip": "Έξυπνη βελτιστοποίηση της πρότασης επεξεργασίας", "model_tip": "Η λειτουργία επεξεργασίας υποστηρίζεται μόνο από τις εκδόσεις V_2 και V_2_TURBO", "number_images_tip": "Αριθμός των αποτελεσμάτων επεξεργασίας που θα δημιουργηθούν", "rendering_speed_tip": "Ελέγχει την ισορροπία μετα<PERSON>ύ ταχύτητας και ποιότητας απόδοσης, εφαρμόζεται μόνο στην έκδοση V_3", "seed_tip": "Έλεγχος της τυχαιότητας στα αποτελέσματα επεξεργασίας", "style_type_tip": "Ο τύπος στυλ για την επεξεργασμένη εικόνα, ισχύει μόνο για την έκδοση V_2 και νεότερες"}, "generate": {"magic_prompt_option_tip": "Έξυπνη βελτιστοποίηση της προτροπής για βελτίωση των αποτελεσμάτων", "model_tip": "Έκδοση μοντέλου: Το V2 είναι το τελευτα<PERSON>ο μοντέλο διεπαφής, το V2A είναι γρήγορο μοντέλο, το V_1 είναι το αρχικό μοντέλο και το _TURBO είναι η επιταχυνόμενη έκδοση", "negative_prompt_tip": "Περιγράψτε στοιχεία που δεν θέλετε να εμφανίζονται στην εικόνα, υποστηρίζεται μόνο στις εκδόσεις V_1, V_1_TURBO, V_2 και V_2_TURBO", "number_images_tip": "Αριθμός εικόνων ανά παραγωγή", "person_generation": "Δημιουργ<PERSON>α προσώπου", "person_generation_tip": "Επιτρέπει στο μοντέλο να δημιουργεί εικόνες προσώπων", "rendering_speed_tip": "Ελέγχει την ισορροπία μεταξύ ταχύτητας και ποιότητας απόδοσης, ισχύει μόνο για την έκδοση V_3", "seed_tip": "Ελέγχει την τυχαιότητα της δημιουργίας εικόνας, χρησιμοποιείται για να επαναληφθεί το ίδιο αποτέλεσμα", "style_type_tip": "Στυλ δημιουργ<PERSON><PERSON>ς εικόνας, ισχύει μόνο για την έκδοση V_2 και μεταγενέστερες"}, "generated_image": "Δημιουργ<PERSON>α εικόνας", "go_to_settings": "Πηγαίνετε στις ρυθμίσεις", "guidance_scale": "Κλίμακα προσαρμογής", "guidance_scale_tip": "<PERSON><PERSON><PERSON><PERSON><PERSON> κλά<PERSON><PERSON><PERSON><PERSON> προσαρμογής. Ελέγχει την προσαρμογή του μοντέλου στην αναζήτηση παρόμοιων εικόνων για το σχόλιο.", "image.size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εικόνας", "image_file_required": "Π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ανεβάστε πρώτα μια εικόνα", "image_file_retry": "Π<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON> ανεβά<PERSON>τε ξανά την εικόνα", "image_handle_required": "Π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ανεβάστε πρώτα μια εικόνα", "image_placeholder": "Δεν υπάρχει εικόνα για τη στιγμή", "image_retry": "Δοκιμάστε ξανά", "image_size_options": {"auto": "Αυτόματο"}, "inference_steps": "Βήματα επεξεργασίας", "inference_steps_tip": "Το πλήθος των βημάτων επεξεργασίας που πρέπει να εκτελεστούν. Περισσότερα βήματα = χαμηλότερη ποιότητα και μεγαλύτερος χρόνος εκτέλεσης", "input_image": "Εικόνα εισόδου", "input_parameters": "Παράμετροι εισόδου", "learn_more": "Μάθετε περισσότερα", "magic_prompt_option": "Ενίσχυση προτροπής", "mode": {"edit": "Επεξεργασία", "generate": "Δημιουργία", "remix": "Ανάμειξη", "upscale": "Μεγέθυνση"}, "model": "Έκδοση", "model_and_pricing": "Μοντέλ<PERSON> και τιμές", "moderation": "Ευαισθησία", "moderation_options": {"auto": "Αυτόματο", "low": "Χαμηλό"}, "negative_prompt": "Αντίστροφη προσδοκία", "negative_prompt_tip": "Περιγράψτε τα πράγματα που δεν θέλετε να εμφανίζονται στην εικόνα", "no_image_generation_model": "Δεν υπάρχει διαθέσιμο μοντέλο δημιουργίας εικόνας. Προσθέστε ένα μοντέλο και ορίστε τον τύπο τερματικού σημείου ως {{endpoint_type}}", "number_images": "Ποσότητα δημιουργιών", "number_images_tip": "Ποσότητα εικόνων που θα δημιουργηθούν μια φορά (1-4)", "paint_course": "Εκπαίδευση", "per_image": "Ανά εικόνα", "per_images": "Ανά εικόνα", "person_generation_options": {"allow_adult": "Να επιτρέπεται ενήλικας", "allow_all": "Να επιτρέπονται όλα", "allow_none": "Να μην επιτρέπεται τίποτα"}, "pricing": "Τιμολόγηση", "prompt_enhancement": "Βελτιστοποίηση σχόλιου", "prompt_enhancement_tip": "Όταν ενεργοποιηθεί, η προσδοκία προσαρμόζεται για να γίνει περισσότερο λεπτομερής και συμβατή με το μοντέλο", "prompt_placeholder": "Περιγράψτε την εικόνα που θέλετε να δημιουργήσετε, για παράδειγμα: ένα ηρωϊκό λιμάνι, το δείπνο του θεού, με απέναντι την ορεινή περιοχή", "prompt_placeholder_edit": "Εισάγετε την περιγραφή της εικόνας σας, χρησιμοποιήστε διπλά εισαγωγικά \"\" για κείμενο", "prompt_placeholder_en": "Εισαγάγετε την περιγραφή εικόνας στα «Αγγλικά», η Imagen υποστηρίζει μόνο αγγλικές εντολές προς το παρόν", "proxy_required": "Αυτή τη στιγμή χρειάζεται να ενεργοποιήσετε τον μεσολαβητή (proxy) για να δείτε τις δημιουργημένες εικόνες. Στο μέλλον θα υποστηρίζεται η άμεση σύνδεση στην Κίνα", "quality": "Ποιότητα", "quality_options": {"auto": "Αυτόματο", "high": "Υψηλό", "low": "Χαμηλό", "medium": "Μεσαίο"}, "regenerate.confirm": "Αυτό θα επιβάλει τις δημιουργίες που έχετε κάνει, θέλετε να συνεχίσετε;", "remix": {"image_file": "Εικόνα αναφοράς", "image_weight": "<PERSON><PERSON><PERSON><PERSON> εικόν<PERSON>ς αναφορ<PERSON>ς", "image_weight_tip": "Ρυθμίστε την επίδραση της εικόνας αναφοράς", "magic_prompt_option_tip": "Έξυπνη βελτιστοποίηση της προτροπής remix", "model_tip": "Επιλέξτε την έκδοση του AI μοντέλου για χρήση σε remix", "negative_prompt_tip": "Περιγρ<PERSON><PERSON><PERSON><PERSON> στοιχεία που δεν θέλετε να εμφανιστούν στο αποτέλεσμα remix", "number_images_tip": "Αριθμός αποτελεσμάτων remix που θα δημιουργηθούν", "rendering_speed_tip": "Ελέγχει την ισορροπία μετα<PERSON>ύ ταχύτητας και ποιότητας απόδοσης, εφαρμόζεται μόνο στην έκδοση V_3", "seed_tip": "Έλεγχος τυχαιότητας των αποτελεσμάτων remix", "style_type_tip": "Στυλ εικόνας μετά το remix, διαθέσιμο μόνο για εκδόσεις V_2 και νεότερες"}, "rendering_speed": "Ταχύτητα απόδοσης", "rendering_speeds": {"default": "Προεπιλογή", "quality": "Υψηλή ποιότητα", "turbo": "Γρήγορα"}, "req_error_model": "Αποτυχ<PERSON>α λήψης μοντέλου", "req_error_no_balance": "Ελέγξτε την εγκυρότητα του token", "req_error_text": "Ο διακομιστής είναι απασχολημένος ή η εντολή περιέχει «λέξεις πνευματικής ιδιοκτησίας» ή «ευαίσθητες λέξεις». Παρακαλούμε δοκιμάστε ξανά.", "req_error_token": "Ελέγξτε την εγκυρότητα του token", "required_field": "Υποχρεωτικό πεδίο", "seed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> παράγοντας", "seed_desc_tip": "Οι ίδιοι σπόρος και εντολή μπορούν να δημιουργήσουν παρόμοιες εικόνες. Ορίστε -1 για διαφορετική εικόνα κάθε φορά", "seed_tip": "Η χρήση του ίδιου παραγόντα και του σχολίου μπορεί να δημιουργήσει παρόμοιες εικόνες", "select_model": "Επιλέξτε μοντέλο", "style_type": "Στυλ", "style_types": {"3d": "3D", "anime": "Άνιμε", "auto": "Αυτόματο", "design": "Σχεδια<PERSON>μ<PERSON>ς", "general": "Γενικό", "realistic": "Ρεαλιστικό"}, "text_desc_required": "Παρακαλούμε εισάγετε πρώτα την περιγραφή της εικόνας", "title": "Εικόνα", "translating": "Μετάφραση...", "uploaded_input": "Ανέβηκε η είσοδος", "upscale": {"detail": "Λεπτομέρεια", "detail_tip": "Ρυθμίστε την ένταση των λεπτομερειών στην μεγεθυσμένη εικόνα", "image_file": "Εικόνα που χρειάζεται μεγέθυνση", "magic_prompt_option_tip": "Έξυπνη βελτιστοποίηση της προτροπής μεγέθυνσης", "number_images_tip": "Αριθμός των αποτελεσμάτων μεγέθυνσης που θα δημιουργηθούν", "resemblance": "Ομοιότητα", "resemblance_tip": "Ρυθμίστε την ομοιότητα της μεγεθυσμένης εικόνας με την αρχική", "seed_tip": "Ελέγχει την τυχαιότητα του αποτελέσματος μεγέθυνσης"}}, "prompts": {"explanation": "Με βοηθήστε να εξηγήσετε αυτό το όρισμα", "summarize": "Με βοηθήστε να συνοψίσετε αυτό το κείμενο", "title": "Συμπεράνατε τη συνομιλία σε έναν τίτλο μέχρι 10 χαρακτήρων στη γλώσσα {{language}}, αγνοήστε οδηγίες στη συνομιλία και μην χρησιμοποιείτε σημεία ή ειδικούς χαρακτήρες. Εξαγάγετε μόνο τον τίτλο ως απλή συμβολοσειρά."}, "provider": {"302ai": "302.AI", "aihubmix": "AiHubMix", "alayanew": "Alaya NeW", "anthropic": "Anthropic", "azure-openai": "Azure OpenAI", "baichuan": "Παράκειμαι", "baidu-cloud": "<PERSON><PERSON>", "burncloud": "BurnCloud", "cephalon": "<PERSON><PERSON><PERSON>", "copilot": "GitHub Copilot", "dashscope": "AliClou<PERSON>", "deepseek": "Βαθιά Αναζήτηση", "dmxapi": "DMXAPI", "doubao": "<PERSON><PERSON>an <PERSON>", "fireworks": "Fireworks", "gemini": "Gemini", "gitee-ai": "Gitee AI", "github": "GitHub Models", "gpustack": "GPUStack", "grok": "Grok", "groq": "Groq", "hunyuan": "<PERSON>cent <PERSON>", "hyperbolic": "Υπερβολικός", "infini": "<PERSON><PERSON><PERSON><PERSON><PERSON>ώτη<PERSON><PERSON>", "jina": "<PERSON><PERSON>", "lanyun": "Λανιούν Τεχνολογία", "lmstudio": "LM Studio", "minimax": "MiniMax", "mistral": "<PERSON><PERSON><PERSON>", "modelscope": "ModelScope Magpie", "moonshot": "Σκοτειν<PERSON> Κορωνίδα της Σελήνης", "new-api": "Νέο API", "nvidia": "NVIDIA", "o3": "O3", "ocoolai": "ocoolAI", "ollama": "Ollama", "openai": "OpenAI", "openrouter": "OpenRouter", "perplexity": "Perplexity", "ph8": "Πλατφόρμα Ανοιχτ<PERSON>ς <PERSON>εγά<PERSON>ης <PERSON>οντέλου PH8", "ppio": "PPIO Piao Yun", "qiniu": "<PERSON><PERSON>", "qwenlm": "QwenLM", "silicon": "Σιδηρική Παρουσία", "stepfun": "Βήμα Ουράς", "tencent-cloud-ti": "Tencent Cloud TI", "together": "Together", "tokenflux": "TokenFlux", "vertexai": "Vertex AI", "voyageai": "Voyage AI", "xirang": "China Telecom Xiran", "yi": "Zero One Wanyiwu", "zhinao": "360 Intelligent Brain", "zhipu": "Zhipu AI"}, "restore": {"confirm": "Είστε σίγουροι ότι θέλετε να επαναφέρετε τα δεδομένα;", "confirm.button": "Επιλογή αρχείου εφαρμογής", "content": "Η επαναφορά θα χρησιμοποιήσει τα αντίγραφα ασφαλείας για να επικαλύψει όλα τα σημερινά δεδομένα εφαρμογής. Παρακαλούμε σημειώστε ότι η διαδικασία μπορεί να χρειαστεί λίγο καιρό, ευχαριστούμε για την υπομονή.", "progress": {"completed": "Η αποκατάσταση ολοκληρώθηκε", "copying_files": "Αντιγραφή αρχείων... {{progress}}%", "extracting": "Εξtraction της αντιγραφής...", "preparing": "Ήταν προετοιμασία για την αποκατάσταση...", "reading_data": "Ανάγνωση δεδομένων...", "title": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON> αποκατάστασης"}, "title": "Επαναφορ<PERSON> Δεδομένων"}, "selection": {"action": {"builtin": {"copy": "Αντιγραφή", "explain": "Εξήγηση", "quote": "Παράθεση", "refine": "Βελτίωση", "search": "Αναζήτηση", "summary": "Σύνοψη", "translate": "Μετάφραση"}, "translate": {"smart_translate_tips": "Έξυπνη μετάφραση: το περιεχόμενο θα μεταφραστεί προτεραιακά στη στόχος γλώσσα· αν το περιεχόμενο είναι ήδη στη στόχος γλώσσα, θα μεταφραστεί στην εναλλακτική γλώσσα"}, "window": {"c_copy": "Αντιγραφή C", "esc_close": "Esc Κλείσιμο", "esc_stop": "Esc Διακοπή", "opacity": "Διαφάνεια παραθύρου", "original_copy": "Αντιγραφή πρωτότυπου", "original_hide": "Απόκρυψη πρωτότυπου", "original_show": "Εμφάνιση πρωτότυπου", "pin": "Καρφίτσωμα", "pinned": "Καρφιτσωμένο", "r_regenerate": "R Επαναδημιουργία"}}, "name": "Β<PERSON><PERSON><PERSON><PERSON><PERSON>πιλογής Λέξεων", "settings": {"actions": {"add_tooltip": {"disabled": "Έχει επιτευχθεί το ανώτατο όριο προσαρμοσμένων λειτουργιών ({{max}})", "enabled": "Προσθήκη προσαρμοσμένης λειτουργίας"}, "custom": "Προσαρμοσμένη λειτουργία", "delete_confirm": "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτήν την προσαρμοσμένη λειτουργία;", "drag_hint": "Σύρετε για ταξινόμηση, μετα<PERSON><PERSON>νήστε προς τα πάνω για να ενεργοποιήσετε τη λειτουργία ({{enabled}}/{{max}})", "reset": {"button": "Επαναφορά", "confirm": "Είστε βέβαιοι ότι θέλετε να επαναφέρετε στην προεπιλεγμένη λειτουργία; Οι προσαρμοσμένες λειτουργίες δεν θα διαγραφούν.", "tooltip": "Επανα<PERSON><PERSON><PERSON><PERSON> στην προεπιλεγμένη λειτουργία, οι προσαρμοσμένες λειτουργίες δεν θα διαγραφούν"}, "title": "Λειτουργία"}, "advanced": {"filter_list": {"description": "Προηγμένες λειτουργίες, προ<PERSON><PERSON><PERSON>ν<PERSON>ται για χρήστες με εμπειρία να ρυθμίσουν μόνο αν καταλαβαίνουν τι κάνουν", "title": "Λίστ<PERSON> Φιλτραρίσματος"}, "filter_mode": {"blacklist": "Μαύρη Λίστα", "default": "Απενεργοποίηση", "description": "Μπορείτε να περιορίσετε το βοηθό επιλογής κειμένου να λειτουργεί μόνο σε συγκεκριμένες εφαρμογές (λευ<PERSON><PERSON> λίστα) ή να μην λειτουργεί (μαύρη λίστα)", "title": "Φιλτράρισμα Εφαρμογών", "whitelist": "Λευκή Λίστα"}, "title": "Προηγ<PERSON><PERSON>νος"}, "enable": {"description": "Η υποστήριξη περιορίζεται αυτή τη στιγμή σε Windows & macOS", "mac_process_trust_hint": {"button": {"go_to_settings": "Μετάβαση στις ρυθμίσεις", "open_accessibility_settings": "Άνοιγμα ρυθμίσεων προσβασιμότητας"}, "description": {"0": "Το βοήθημα επιλογής λέξεων χρειάζεται «<strong>άδεια πρόσβασης σε δυνατότητες υποστήριξης</strong>» για να λειτουργήσει σωστά.", "1": "Παρακαλ<PERSON>ύμε κάντε κλικ στο «<strong>Πήγαινε στις ρυθμίσεις</strong>» και, στη συνέχεια, στο παράθυρο αιτήματος αδειών που θα εμφανιστε<PERSON>, κάν<PERSON>ε κλικ στο κουμπί «<strong>Άνοιγμα ρυθμίσεων συστήματος</strong>», βρείτε στη λίστα εφαρμογών που θα ακολουθήσει το «<strong>Cherry Studio</strong>» και ενεργοποιήστε την άδεια.", "2": "Μετά την ολοκλήρωση των ρυθμίσεων, ενεργοποιήστε ξανά το βοήθημα επιλογής λέξεων."}, "title": "Άδεια Προσβασιμότητας"}, "title": "Ενεργοποίηση"}, "experimental": "Πειραματική λειτουργία", "filter_modal": {"title": "Λίστα Εφαρ<PERSON>ογών Φιλτραρίσματος", "user_tips": {"mac": "Παρα<PERSON><PERSON><PERSON><PERSON> εισαγάγετε το Bundle ID της εφαρμογής, έ<PERSON><PERSON> <PERSON>νά γραμμή, δεν γίνεται διάκριση πεζών/κεφαλα<PERSON>ων, υποστηρίζεται ασαφής αντιστοίχιση. Για παράδειγμα: com.google.Chrome, com.apple.mail κ.λπ.", "windows": "Παρα<PERSON><PERSON><PERSON><PERSON> εισαγάγετε το όνομα του εκτελέσιμου αρχείου της εφαρμογής, έν<PERSON> ανά γραμμή, δεν γίνεται διάκριση πεζών/κεφαλα<PERSON>ων, υποστηρίζεται ασαφής αντιστοίχιση. Για παράδειγμα: chrome.exe, weixin.exe, Cherry Studio.exe κ.λπ."}}, "search_modal": {"custom": {"name": {"hint": "Π<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε το όνομα της μηχανής αναζήτησης", "label": "Προσαρμοσμένο Όνομα", "max_length": "Το όνομα δεν μπορεί να ξεπερνά τους 16 χαρακτήρες"}, "test": "Δοκιμή", "url": {"hint": "Χρησιμοποιήστε {{queryString}} για να αντιπροσωπεύσετε τον όρο αναζήτησης", "invalid_format": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε μια έγκυρη διεύθυνση URL που ξεκινά με http:// ή https://", "label": "Προσαρμοσμένη διεύθυνση URL αναζήτησης", "missing_placeholder": "Η διεύθυνση URL πρέπει να περιλαμβάνει τον συμπληρωτή θέσης {{queryString}}", "required": "Π<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τη διεύθυνση URL αναζήτησης"}}, "engine": {"custom": "Προσαρμογή", "label": "Μηχανή Αναζήτησης"}, "title": "Ρύθμιση μηχανής αναζήτησης"}, "toolbar": {"compact_mode": {"description": "Σε συμπαγ<PERSON> λειτουργ<PERSON><PERSON>, εμ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>α<PERSON> μόνο εικονίδια, χωρ<PERSON>ς κείμενο", "title": "Συμ<PERSON><PERSON><PERSON><PERSON><PERSON>τουργία"}, "title": "Γραμμή εργαλείων", "trigger_mode": {"ctrlkey": "Πλήκτρ<PERSON> Ctrl", "ctrlkey_note": "Επιλέξτε μια λέξη και, στη συνέχεια, κρατήστε πατημένο το πλήκτρο Ctrl για να εμφανιστεί η γραμμή εργαλείων", "description": "Ο τρόπος ενεργοποίησης της λήψης λέξεων και εμφάνισης της γραμμής εργαλείων μετά την επιλογή", "description_note": {"mac": "Αν έχετε αντιστοιχίσει εκ νέου το πλήκτρο ⌘ μέσω συντομεύσεων ή εργαλείων αντιστοίχισης πλήκτρων, ενδ<PERSON><PERSON><PERSON><PERSON><PERSON>ι να μην είναι δυνατή η επιλογή λέξεων σε ορισμένες εφαρμογές.", "windows": "Λίγες εφαρμογές δεν υποστηρίζουν την επιλογή λέξεων μέσω του πλήκτρου Ctrl. Αν έχετε αντιστοιχίσει εκ νέου το πλήκτρο Ctrl μέσω εργαλείων αντιστοίχισης πλήκτρων όπως το AHK, ενδέχεται να μην είναι δυνατή η επιλογή λέξεων σε ορισμένες εφαρμογές."}, "selected": "Επιλογή λέξης", "selected_note": "Η γραμμή εργαλείων εμφανίζεται αμέσως μετά την επιλογή λέξης", "shortcut": "Συντόμευση", "shortcut_link": "Μετάβαση στις ρυθμίσεις συντομεύσεων", "shortcut_note": "Μετά την επιλογή λέξης, χρησιμοποιήστε τη συντόμευση για να εμφανίσετε τη γραμμή εργαλείων. Ορίστε τη συντόμευση λήψης λέξεων και ενεργοποιήστε την από τη σελίδα ρυθμίσεων συντομεύσεων.", "title": "Τρόπος λήψης λέξεων"}}, "user_modal": {"assistant": {"default": "Προεπιλογή", "label": "Επιλέξτε βοηθό"}, "icon": {"error": "Μη έγκυρο όνομα εικονιδίου, ελέγξτε την εισαγωγή", "label": "Εικονίδιο", "placeholder": "Εισαγωγ<PERSON> ον<PERSON><PERSON>α<PERSON>ος εικονιδί<PERSON><PERSON>", "random": "Τυ<PERSON><PERSON><PERSON><PERSON> εικονίδιο", "tooltip": "Το όνομα εικονιδ<PERSON><PERSON><PERSON> είναι με πεζά, π.χ. arrow-right", "view_all": "Προβολή όλων των εικονιδίων"}, "model": {"assistant": "Χρή<PERSON>η βοηθού", "default": "Προεπιλεγ<PERSON><PERSON>νο μοντέλο", "label": "<PERSON>ο<PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": "Χρήση βοηθού: θα χρησιμοποιηθούν τα συστηματικά ερεθίσματα του βοηθού και οι παράμετροι μοντέλου ταυτόχρονα"}, "name": {"hint": "Παρα<PERSON><PERSON><PERSON><PERSON> εισαγάγετε το όνομα της λειτουργίας", "label": "Όνομα"}, "prompt": {"copy_placeholder": "Αντιγραφή προτύπου", "label": "Ερέθισμα χρήστη (Prompt)", "placeholder": "Χρησιμοποιήστε το πρότυπο {{text}} για να αντιπροσωπεύσετε το επιλεγμένο κείμενο· αν δεν συμπληρωθεί, το επιλεγμένο κείμενο θα προστεθεί στο τέλος αυτού του ερεθίσματος", "placeholder_text": "Πρότυπο", "tooltip": "Ερέθισμα χρήστη, που χρησιμοποιείται ως πρόσθετη πληροφορία εισόδου για τον χρήστη, δεν αντικαθιστά το σύστημα ερεθίσματος του βοηθού"}, "title": {"add": "Προσθήκη προσαρμοσμένης λειτουργίας", "edit": "Επεξεργασία προσαρμοσμένης λειτουργίας"}}, "window": {"auto_close": {"description": "Το παράθυρο θα κλείσει αυτόματα όταν δεν είναι στο προσκήνιο και χάσει την εστίαση", "title": "Αυτόματο Κλείσιμο"}, "auto_pin": {"description": "Βάζει το παράθυρο στην κορυφή από προεπιλογή", "title": "Αυτόματη Επικορώνωση"}, "follow_toolbar": {"description": "Η θέση του παραθύρου θα εμφανίζεται μαζί με τη γραμμή εργαλείων· αν απενεργοποιηθεί, θα εμφανίζεται πάντα στο κέντρο", "title": "Ακολούθηση Γραμμής Εργαλείων"}, "opacity": {"description": "Ορίστε την προεπιλεγμένη διαφάνεια του παραθύρου, 100% σημαίνει πλήρως αδιαφανές", "title": "Διαφάνεια"}, "remember_size": {"description": "Κατά τη διάρκεια της εκτέλεσης της εφαρμογής, το παράθυρο θα εμφανίζεται με το μέγεθος που ορίστηκε τελευταία φορά", "title": "Να θυμάσαι το μέγεθος"}, "title": "Παράθυρο λειτουργίας"}}}, "settings": {"about": "Περί μας", "about.checkUpdate": "Έλεγχος ενημερώσεων", "about.checkUpdate.available": "Άμεση ενημέρωση", "about.checkingUpdate": "Ελέγχω ενημερώσεις...", "about.contact.button": "Ταχυδρομείο", "about.contact.title": "Επικοινων<PERSON>α μέσω ταχυδρομείου", "about.debug.open": "Άνοιγμα", "about.debug.title": "Πίν<PERSON><PERSON><PERSON><PERSON> Αποσφαλμάτωσης", "about.description": "Ένα AI ασιστάντα που έχει σχεδιαστεί για δημιουργούς", "about.downloading": "Λή<PERSON>η ενημερώσεων...", "about.feedback.button": "Σχόλια και Παρατηρήσεις", "about.feedback.title": "Αποστολή σχολίων", "about.license.button": "Προβολή", "about.license.title": "Licenses", "about.releases.button": "Προβολή", "about.releases.title": "Ημερολόγιο Ενημερώσεων", "about.social.title": "Κοινωνικά Λογαριασμοί", "about.title": "Περί μας", "about.updateAvailable": "Νέα έκδοση {{version}} εντοπίστηκε", "about.updateError": "Σφάλμα κατά την ενημέρωση", "about.updateNotAvailable": "Το λογισμικ<PERSON> σας είναι ήδη στην πιο πρόσφατη έκδοση", "about.website.button": "Προβολή", "about.website.title": "Ιστοσελίδα", "advanced.auto_switch_to_topics": "Αυτόματη μετάβαση σε θέματα", "advanced.title": "Ρυθμίσεις Ανώτερου Νiveau", "assistant": "Πρόεδρος Υπηρεσίας", "assistant.icon.type": "<PERSON><PERSON><PERSON><PERSON> εικονιδίου μοντέλου", "assistant.icon.type.emoji": "<PERSON><PERSON><PERSON>", "assistant.icon.type.model": "Εικο<PERSON><PERSON><PERSON><PERSON><PERSON> μοντέλου", "assistant.icon.type.none": "Κανένα", "assistant.model_params": "Παράμετροι Μοντέλου", "assistant.title": "Πρόεδρος Υπηρεσίας", "data": {"app_data": "Δεδομένα εφαρμογής", "app_data.copy_data_option": "Αντιγραφ<PERSON> δεδομένων, θα γίνει αυτόματα επανεκκίνηση και τα δεδομένα από τον αρχικό κατάλογο θα αντιγραφούν στο νέο κατάλογο", "app_data.copy_failed": "Αποτυχ<PERSON>α αντιγραφής δεδομένων", "app_data.copy_success": "Τα δεδομένα αντιγράφηκαν επιτυχώς στη νέα τοποθεσία", "app_data.copy_time_notice": "Η αντιγραφή δεδομένων θα διαρκέσει κάποιο χρονικό διάστημα, μην κλείσετε την εφαρμογή κατά τη διάρκεια της αντιγραφής", "app_data.copying": "Γίνεται αντιγραφή δεδομένων στη νέα τοποθεσία...", "app_data.copying_warning": "Η αντιγραφή δεδομένων βρίσκεται σε εξέλιξη, μην κλείσετε την εφαρμογή με τη βία. Η εφαρμογή θα επανεκκινήσει αυτόματα μετά την ολοκλήρωση της αντιγραφής", "app_data.migration_title": "Μεταφορ<PERSON> δεδομένων", "app_data.new_path": "Νέα διαδρομή", "app_data.original_path": "Αρχική διαδρομή", "app_data.path_changed_without_copy": "Η διαδρομή άλλαξε επιτυχώς", "app_data.restart_notice": "Η εφαρμογή μπορεί να επανεκκινήσει πολλές φορές για να εφαρμοστούν οι αλλαγές", "app_data.select": "Αλλαγ<PERSON> καταλόγου", "app_data.select_error": "Αποτυχία αλλαγής καταλόγου δεδομένων", "app_data.select_error_in_app_path": "Η νέα διαδρομή είναι ίδια με τη διαδρομή εγκατάστασης της εφαρμογής. Επιλέξτε άλλη διαδρομή", "app_data.select_error_root_path": "Η νέα διαδρομή δεν μπορεί να είναι η ριζική διαδρομή", "app_data.select_error_same_path": "Η νέα διαδρομή είναι ίδια με την παλιά. Επιλέξτε άλλη διαδρομή", "app_data.select_error_write_permission": "Η νέα διαδρομή δεν έχει δικαιώματα εγγραφής", "app_data.select_not_empty_dir": "Ο νέος κατάλογος δεν είναι κενός", "app_data.select_not_empty_dir_content": "Ο νέος κατάλογος δεν είναι κενός, τα δεδομένα στον νέο κατάλογο θα αντικατασταθούν, υπάρχε<PERSON> κίνδυνος απώλειας δεδομένων και αποτυχίας αντιγραφής. Θέλετε να συνεχίσετε;", "app_data.select_success": "Ο κατάλογος δεδομένων άλλαξε, η εφαρμογή θα επανεκκινήσει για να εφαρμοστούν οι αλλαγές", "app_data.select_title": "Αλλαγ<PERSON> καταλόγου δεδομένων εφαρμογής", "app_data.stop_quit_app_reason": "Η εφαρμογή προς το παρόν μεταφέρει δεδομένα, δεν μπορείτε να βγείτε", "app_knowledge": "Αρχεία βάσης γνώσεων", "app_knowledge.button.delete": "Διαγρα<PERSON>ή αρχείου", "app_knowledge.remove_all": "Διαγραφή αρχείων βάσης γνώσεων", "app_knowledge.remove_all_confirm": "Η διαγραφή των αρχείων της βάσης γνώσεων μπορεί να μειώσει τη χρήση χώρου αποθήκευσης, αλλά δεν θα διαγράψει τα διανυσματωτικά δεδομένα της βάσης γνώσεων. Μετά τη διαγραφή, δεν θα μπορείτε να ανοίξετε τα αρχεία πηγή. Θέλετε να διαγράψετε;", "app_knowledge.remove_all_success": "Τα αρχεία διαγράφηκαν με επιτυχία", "app_logs": "Φάκελοι εφαρμογής", "app_logs.button": "Άνοιγμα καταγραφής", "backup.skip_file_data_help": "Κατά τη δημιουργία αντιγράφων ασφαλείας, παραλε<PERSON><PERSON>τε τις εικόνες, τις βάσεις γνώσεων και άλλα αρχεία δεδομένων. Δημιουργήστε αντίγραφα μόνο για το ιστορικό συνομιλιών και τις ρυθμίσεις. Αυτό θα μειώσει τη χρήση χώρου και θα επιταχύνει την ταχύτητα δημιουργίας αντιγράφων.", "backup.skip_file_data_title": "Συμπυκνωμένο αντίγραφο ασφαλείας", "clear_cache": {"button": "Καθαρισμός Μνήμης", "confirm": "Η διαγραφή της μνήμης θα διαγράψει τα στοιχεία καθαρισμού της εφαρμογής, συμπεριλαμβανομένων των στοιχείων πρόσθετων εφαρμογών. Αυτή η ενέργεια δεν είναι αναστρέψιμη. Θέλετε να συνεχίσετε;", "error": "Αποτυχία καθαρισμού της μνήμης", "success": "Η μνήμη καθαρίστηκε με επιτυχία", "title": "Καθαρισμός Μνήμης"}, "data.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> δεδομένων", "divider.basic": "Ρυθμίσεις βασικών δεδομένων", "divider.cloud_storage": "Ρυθμίσεις αποθήκευσης στο νέφος", "divider.export_settings": "Ρυθμίσεις εξαγωγής", "divider.third_party": "Σύνδεση τρίτων", "export_menu": {"docx": "Εξαγωγή σε Word", "image": "Εξαγωγή ως εικόνα", "joplin": "Εξαγωγ<PERSON> στο <PERSON>", "markdown": "Εξαγωγή σε Markdown", "markdown_reason": "Εξαγωγή σε Markdown (περιλαμβάνει σκέψη)", "notion": "Εξαγωγή στο Notion", "obsidian": "Εξαγωγή στο Obsidian", "plain_text": "Αντιγραφ<PERSON> ως απλό κείμενο", "siyuan": "Εξαγωγή στο Ση-Υάν", "title": "Εξαγωγή ρυθμίσεων μενού", "yuque": "Εξαγωγή στο Yuque"}, "hour_interval_one": "{{count}} ώρα", "hour_interval_other": "{{count}} ώρες", "joplin": {"check": {"button": "Έλεγχος", "empty_token": "Παρακαλούμε εισάγετε τον κωδικό προσβασιμότητας του <PERSON><PERSON><PERSON>", "empty_url": "Παρακαλούμε εισάγετε την URL που είναι συνδεδεμένη με την υπηρεσία κοπής του <PERSON><PERSON><PERSON>", "fail": "Η επαλήθευση σύνδεσης του <PERSON><PERSON><PERSON> απέτυχε", "success": "Η επαλήθευση σύνδεσης του <PERSON><PERSON><PERSON><PERSON>αν επιτυχής"}, "export_reasoning.help": "Όταν είναι ενεργοποιημένο, θα συμπεριλαμβάνει το περιεχόμενο της αλυσίδας σκέψης κατά την εξαγωγή στο <PERSON>.", "export_reasoning.title": "Συμπερίληψη Αλυσίδας Σκέψης κατά την Εξαγωγή", "help": "Σ τις επιλογές τ<PERSON><PERSON>, εν<PERSON><PERSON>γ<PERSON><PERSON><PERSON><PERSON>ήστε την υπηρεσία περικοπής ιστότοπων (χωρ<PERSON>ς εγκατάσταση πρόσθετων στο περιηγητή), επιβ<PERSON>βαιώστε τον θύραρι και αντιγράψτε τον κωδικό πρόσβασης.", "title": "Ρύθμιση <PERSON><PERSON><PERSON>", "token": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβα<PERSON><PERSON><PERSON>", "token_placeholder": "Εισαγάγετε τον κωδικό πρόσβα<PERSON><PERSON><PERSON>", "url": "URL υπηρεσίας περικο<PERSON><PERSON><PERSON>", "url_placeholder": "http://127.0.0.1:41184/"}, "local": {"autoSync": "Αυτόματο αντίγραφο ασφαλείας", "autoSync.off": "Απενεργοποίηση", "backup.button": "Τοπικ<PERSON> αντίγραφο ασφαλείας", "backup.manager.columns.actions": "Ενέργειες", "backup.manager.columns.fileName": "Όνομα αρχείου", "backup.manager.columns.modifiedTime": "Ώρα τροποποίησης", "backup.manager.columns.size": "Μέγεθος", "backup.manager.delete.confirm.multiple": "Είστε βέβαιοι ότι θέλετε να διαγράψετε τα {{count}} επιλεγμένα αρχεία αντιγράφων ασφαλείας; Η ενέργεια αυτή δεν μπορεί να αναιρεθεί.", "backup.manager.delete.confirm.single": "Είστε βέβαιοι ότι θέλετε να διαγράψετε το αρχείο αντιγράφου ασφαλείας \"{{fileName}}\"; Η ενέργεια αυτή δεν μπορεί να αναιρεθεί.", "backup.manager.delete.confirm.title": "Επιβεβαίωση διαγραφής", "backup.manager.delete.error": "Η διαγραφή απέτυχε", "backup.manager.delete.selected": "Διαγραφή επιλεγμένων", "backup.manager.delete.success.multiple": "Διαγράφηκαν {{count}} αρχεία αντιγράφων ασφαλείας", "backup.manager.delete.success.single": "Η διαγραφή ήταν επιτυχής", "backup.manager.delete.text": "Διαγραφή", "backup.manager.fetch.error": "Αποτυχ<PERSON><PERSON> λήψης αρχείων αντιγράφων ασφαλείας", "backup.manager.refresh": "Ανανέωση", "backup.manager.restore.error": "Η αποκατάσταση απέτυχε", "backup.manager.restore.success": "Η αποκατάστα<PERSON>η ήταν επιτυχής, η εφαρμογή θα ανανεωθεί σύντομα", "backup.manager.restore.text": "Αποκατάσταση", "backup.manager.select.files.delete": "Επιλέξτε τα αρχεία αντιγράφων ασφαλείας που θέλετε να διαγράψετε", "backup.manager.title": "Διαχείριση αρχείων αντιγράφων ασφαλείας", "backup.modal.filename.placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το όνομα του αρχείου αντιγράφου ασφαλείας", "backup.modal.title": "Τοπικ<PERSON> αντίγραφο ασφαλείας", "directory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αντι<PERSON><PERSON><PERSON><PERSON>ων ασφαλείας", "directory.placeholder": "Επιλέξτε κατάλο<PERSON><PERSON> αντιγράφων ασφαλείας", "directory.select_error_app_data_path": "Η νέα διαδρομή δεν μπορεί να είναι ίδια με τη διαδρομή δεδομένων της εφαρμογής", "directory.select_error_in_app_install_path": "Η νέα διαδρομή δεν μπορεί να είναι ίδια με τη διαδρομή εγκατάστασης της εφαρμογής", "directory.select_error_write_permission": "Η νέα διαδρομή δεν έχει δικαιώματα εγγραφής", "directory.select_title": "Επιλογή καταλόγου αντιγράφων ασφαλείας", "hour_interval_one": "{{count}} ώρα", "hour_interval_other": "{{count}} ώρες", "lastSync": "Τελευτ<PERSON><PERSON><PERSON> αντίγραφο ασφαλείας", "maxBackups": "Μέγιστος αριθμός αντιγράφων ασφαλείας", "maxBackups.unlimited": "Απεριόριστα", "minute_interval_one": "{{count}} λεπτό", "minute_interval_other": "{{count}} λεπτά", "noSync": "Αναμονή για το επόμενο αντίγραφο ασφαλείας", "restore.button": "Διαχείριση αρχείων αντιγράφων ασφαλείας", "restore.confirm.content": "Η αποκατάσταση από τοπικό αντίγραφο ασφαλείας θα αντικαταστήσει τα τρέχοντα δεδομένα. Θέλετε να συνεχίσετε;", "restore.confirm.title": "Επιβεβαίωση αποκατάστασης", "syncError": "Σφάλμα αντιγράφου ασφαλείας", "syncStatus": "Κατάστα<PERSON>η αντιγράφου ασφαλείας", "title": "Τοπικ<PERSON> αντίγραφο ασφαλείας"}, "markdown_export.force_dollar_math.help": "Κάνοντας το ενεργ<PERSON>, κατά την εξαγωγ<PERSON> Markdown, θα χρησιμοποιείται αναγκαστικά το $$ για να σημειώσετε την εξίσωση LaTeX. Νομίζετε: Αυτή η επιλογή θα επηρεάσει και όλες τις μεθόδους εξαγωγής μέσω Mark<PERSON>, όπως το Notion, Yuyu κλπ.", "markdown_export.force_dollar_math.title": "Ανάγκη χρήσης $$ για να σημειώσετε την εξίσωση LaTeX", "markdown_export.help": "Εάν συμπληρώ<PERSON>ετε, κάθε φορά που θα εξαγάγετε θα αποθηκεύεται αυτόματα σε αυτή τη διαδρομή· διαφορετικά, θα εμφανιστεί μια διαβεβαίωση αποθήκευσης.", "markdown_export.path": "Προεπιλογή διαδρομής εξαγωγής", "markdown_export.path_placeholder": "Διαδρομή εξαγωγής", "markdown_export.select": "Επιλογή", "markdown_export.show_model_name.help": "Όταν ενεργοποιηθε<PERSON>, το όνομα του μοντέλου θα εμφανίζεται κατά την εξαγωγή σε Markdown. Σημείωση: Αυτό επηρεάζει επίσης όλους τους τρόπους εξαγωγής μέσω Markdown, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> κ.λπ.", "markdown_export.show_model_name.title": "Χρή<PERSON><PERSON> ον<PERSON><PERSON>α<PERSON>ος μοντέλου κατά την εξαγωγή", "markdown_export.show_model_provider.help": "Εμφάνιση του παρόχου μοντέλου κατά την εξαγωγή σε Markdown, π.χ. <PERSON><PERSON><PERSON>, <PERSON> κ.λπ.", "markdown_export.show_model_provider.title": "Εμφάνιση παρόχου μοντέλου", "markdown_export.title": "Εξαγωγή Markdown", "message_title.use_topic_naming.help": "Όταν είναι ενεργ<PERSON>, δημιουργεί τίτλους για τα μηνύματα που εξάγονται χρησιμοποιώντας μοντέλο ονομασίας θεμάτων. Αυτό επηρεάζει επίσης όλες τις μεθόδους εξαγωγής μέσω Markdown.", "message_title.use_topic_naming.title": "Δημιουργ<PERSON>α τίτλων μηνυμάτων χρησιμοποιώντας μοντέλο ονομασίας θεμάτων", "minute_interval_one": "{{count}} λεπτά", "minute_interval_other": "{{count}} λεπτά", "notion.api_key": "Κλειδί Notion", "notion.api_key_placeholder": "Εισαγάγετε το κλειδί Notion", "notion.check": {"button": "Έλεγχος", "empty_api_key": "Δεν έχει ρυθμιστεί η κλειδιά API", "empty_database_id": "Δεν έχει ρυθμιστεί ο ID της βάσης δεδομένων", "error": "Σφάλμα σύνδεσης, παρα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε ελέγξτε το δίκτυο και αν το API key και το Database ID είναι σωστά", "fail": "Η σύνδεση απέτυχε, παρα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε ελέγξτε το δίκτυο και αν το API key και το Database ID είναι σωστά", "success": "Η σύνδεση ήταν επιτυχής"}, "notion.database_id": "ID Βάσης Δεδομένων Notion", "notion.database_id_placeholder": "Εισαγάγετε το ID Βάσης Δεδομένων Notion", "notion.export_reasoning.help": "Όταν ενεργοποιηθε<PERSON>, το αλυσίδωμα σκέψης θα συμπεριλαμβάνεται κατά την εξαγωγή στο Notion.", "notion.export_reasoning.title": "Συμπερίληψη αλυσιδώματος σκέψης κατά την εξαγωγή", "notion.help": "Έγχρωστη διαδρομή του Notion", "notion.page_name_key": "Όνομα πεδίου τίτλου σελίδας", "notion.page_name_key_placeholder": "Εισαγάγετε το όνομα του πεδίου τίτλου σελίδας, προεπιλογή: Name", "notion.title": "Ρυθμίσεις του Notion", "nutstore": {"backup.button": "Αντίγραφο ασφαλείας στο <PERSON>", "checkConnection.fail": "Αποτυχία σύνδεσης στο <PERSON>", "checkConnection.name": "Έλεγχος σύνδεσης", "checkConnection.success": "Συνδεδεμένο στο <PERSON>", "isLogin": "Συνδεδεμένος", "login.button": "Σύνδεση", "logout.button": "Αποσύνδεση", "logout.content": "Μετά την αποσύνδεση δεν θα μπορείτε να κάνετε αντίγραφο ασφαλείας ή να ανακτήσετε δεδομένα από το <PERSON> Cloud", "logout.title": "Επιβεβαίωση αποσύνδεσης από το <PERSON>;", "new_folder.button": "<PERSON><PERSON><PERSON>", "new_folder.button.cancel": "Άκυρο", "new_folder.button.confirm": "Επιβεβαίωση", "notLogin": "Μη συνδεδεμένος", "path": "Διαδρομή αποθήκ<PERSON>υ<PERSON><PERSON><PERSON>", "path.placeholder": "Π<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τη διαδρομή αποθήκευσης του <PERSON> Cloud", "pathSelector.currentPath": "Τρέχουσα διαδρομή", "pathSelector.return": "Πίσω", "pathSelector.title": "Διαδρομή αποθήκ<PERSON>υ<PERSON><PERSON><PERSON>", "restore.button": "Επαναφορά από το <PERSON> Cloud", "title": "Ρυθμίσεις <PERSON><PERSON><PERSON>", "username": "Όνομα χρήστη <PERSON><PERSON>n Cloud"}, "obsidian": {"default_vault": "Προεπιλεγμένο αποθετήριο Obsidian", "default_vault_export_failed": "Η εξαγωγή απέτυχε", "default_vault_fetch_error": "Αποτυχ<PERSON><PERSON> ανάκτησης αποθετηρίου Obsidian", "default_vault_loading": "Ανάκτηση αποθετηρίου Obsidian...", "default_vault_no_vaults": "Δεν βρέθηκε αποθετήριο Obsidian", "default_vault_placeholder": "Επιλέξτε προεπιλεγμένο αποθετήριο Obsidian", "title": "Ρύθμιση του Obsidian"}, "s3": {"accessKeyId": "Access Key ID", "accessKeyId.placeholder": "Access Key ID", "autoSync": "Αυτόματη συγχρονισμός", "autoSync.hour": "Κάθε {{count}} ώρες", "autoSync.minute": "Κάθε {{count}} λεπτά", "autoSync.off": "Απενεργοποιημένο", "backup.button": "Άμεση δημιουργία αντιγράφου ασφαλείας", "backup.error": "Η δημιουργία αντιγράφου ασφαλείας στο S3 απέτυχε: {{message}}", "backup.manager.button": "Διαχείριση αντιγράφων ασφαλείας", "backup.modal.filename.placeholder": "Π<PERSON>ρα<PERSON><PERSON><PERSON><PERSON> εισάγετε όνομα αρχείου για το αντίγραφο ασφαλείας", "backup.modal.title": "Αντίγραφο ασφαλείας S3", "backup.operation": "Λειτουρ<PERSON><PERSON><PERSON> αντιγράφου ασφαλείας", "backup.success": "Επιτυχής δημιουργία αντιγράφου ασφαλείας S3", "bucket": "Δοχείο", "bucket.placeholder": "<PERSON><PERSON>, π.χ.: example", "endpoint": "Διεύθυνση API", "endpoint.placeholder": "https://s3.example.com", "manager.close": "Κλείσιμο", "manager.columns.actions": "Ενέργειες", "manager.columns.fileName": "Όνομα αρχείου", "manager.columns.modifiedTime": "Ώρα τροποποίησης", "manager.columns.size": "Μέγεθος αρχείου", "manager.config.incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> συμπληρώστε όλες τις πληροφορίες διαμόρφωσης S3", "manager.delete": "Διαγραφή", "manager.delete.confirm.multiple": "Είστε βέβαιοι ότι θέλετε να διαγράψετε τα {{count}} επιλεγμένα αρχεία αντιγράφων ασφαλείας; Η ενέργεια αυτή δεν μπορεί να αναιρεθεί.", "manager.delete.confirm.single": "Είστε βέβαιοι ότι θέλετε να διαγράψετε το αρχείο αντιγράφου ασφαλείας \"{{fileName}}\"; Η ενέργεια αυτή δεν μπορεί να αναιρεθεί.", "manager.delete.confirm.title": "Επιβεβαίωση διαγραφής", "manager.delete.error": "Αποτυχία διαγραφής αρχείου αντιγράφου ασφαλείας: {{message}}", "manager.delete.selected": "Διαγρα<PERSON>ή επιλεγμένων ({{count}})", "manager.delete.success.multiple": "Επιτυχής διαγραφή {{count}} αρχείων αντιγράφων ασφαλείας", "manager.delete.success.single": "Επιτυχής διαγραφή αρχείου αντιγράφου ασφαλείας", "manager.files.fetch.error": "Αποτυχί<PERSON> λήψης λίστας αρχείων αντιγράφων ασφαλείας: {{message}}", "manager.refresh": "Ανανέωση", "manager.restore": "Επαναφορά", "manager.select.warning": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε τα αρχεία αντιγράφων ασφαλείας που θέλετε να διαγράψετε", "manager.title": "Διαχείριση αρχείων αντιγράφων ασφαλείας S3", "maxBackups": "Μέγιστος αριθμός αντιγράφων ασφαλείας", "maxBackups.unlimited": "Απεριόριστα", "region": "Περιοχή", "region.placeholder": "Region, π.χ.: us-east-1", "restore.config.incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> συμπληρώστε όλες τις πληροφορίες διαμόρφωσης S3", "restore.confirm.cancel": "Ακύρωση", "restore.confirm.content": "Η επαναφορά δεδομένων θα αντικαταστήσει όλα τα τρέχοντα δεδομένα, και η ενέργεια αυτή δεν μπορεί να αναιρεθεί. Είστε βέβαιοι ότι θέλετε να συνεχίσετε;", "restore.confirm.ok": "Επιβεβαίωση επαναφοράς", "restore.confirm.title": "Επιβεβαίωση επαναφοράς δεδομένων", "restore.error": "Η επαναφορά δεδομένων απέτυχε: {{message}}", "restore.file.required": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε το αρχείο αντιγράφου ασφαλείας για επαναφορά", "restore.modal.select.placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε το αρχείο αντιγράφου ασφαλείας για επαναφορά", "restore.modal.title": "Επαναφορ<PERSON> δεδομένων S3", "restore.success": "Επιτυχής επανα<PERSON><PERSON><PERSON><PERSON> δεδομένων", "root": "<PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αντιγράφου ασφαλείας (προαιρετικό)", "root.placeholder": "Π.χ.: /cherry-studio", "secretAccessKey": "Secret Access Key", "secretAccessKey.placeholder": "Secret Access Key", "skipBackupFile": "Ελα<PERSON><PERSON><PERSON> αντίγραφο ασφαλείας", "skipBackupFile.help": "Όταν ενεργοποιηθεί, η δημιουργία αντιγράφου ασφαλείας θα παραλείπει τα δεδομένα αρχείων και θα δημιουργ<PERSON><PERSON> αντίγραφο μόνο τις πληροφορίες ρυθμίσεων, μειώνοντας σημαντικά το μέγεθος του αρχείου αντιγράφου ασφαλείας", "syncStatus": "Κατάσταση συγχρονισμού", "syncStatus.error": "Σφάλμα συγχρονισμού: {{message}}", "syncStatus.lastSync": "Τελευτ<PERSON><PERSON><PERSON> συγχρονισμός: {{time}}", "syncStatus.noSync": "<PERSON><PERSON><PERSON><PERSON><PERSON> συγχρονισμό", "title": "Αποθήκευση συμβατή με S3", "title.help": "Υπηρεσία αποθήκευσης αντικειμένων συμβατή με το API του AWS S3, όπως AWS S3, Cloudflare R2, Alibaba Cloud OSS, Tencent Cloud COS κ.λπ.", "title.tooltip": "Έγγραφα διαμόρφωσης αποθήκευσης συμβατής με S3"}, "siyuan": {"api_url": "Διεύθυνση API", "api_url_placeholder": "Παράδειγμα: http://127.0.0.1:6806", "box_id": "ID Υπολογιστή", "box_id_placeholder": "Εισάγετε το ID υπολογιστή", "check": {"button": "Έλεγχος", "empty_config": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε τη διεύθυνση API και το token", "error": "Αιφνίδια διακο<PERSON>ή σύνδεσης, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε τη σύνδεση δικτύου", "fail": "Αποτυχ<PERSON><PERSON> σύνδεσης, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε τη διεύθυνση API και το token", "success": "Η σύνδεση ήταν επιτυχής", "title": "Έλεγχος Σύνδεσης"}, "root_path": "Κεντρική διαδρομή εγγράφων", "root_path_placeholder": "Παράδειγμα: /CherryStudio", "title": "Ρυθμίσεις του <PERSON>yuan Σημειώσεων", "token": "Κλειδί API", "token.help": "Λήψη α<PERSON><PERSON> Σημειώσεις -> Ρυθμίσεις -> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "token_placeholder": "Εισάγετε το κλειδί των <PERSON><PERSON> Σημειώσεων"}, "title": "Ρυθμίσεις δεδομένων", "webdav": {"autoSync": "Αυτόματη αντιγραφή ασφαλείας", "autoSync.off": "Επιστροφή στο κλειδωμένο κατάσταμμα", "backup.button": "Αντιγραφή ασφαλείας στο WebDAV", "backup.manager.columns.actions": "Ενέργειες", "backup.manager.columns.fileName": "Όνομα αρχείου", "backup.manager.columns.modifiedTime": "Ώρα τροποποίησης", "backup.manager.columns.size": "Μέγεθος", "backup.manager.delete.confirm.multiple": "Είστε βέβαιοι ότι θέλετε να διαγράψετε τα {{count}} επιλεγμένα αντίγραφα ασφαλείας; Η ενέργεια αυτή δεν μπορεί να αναιρεθεί.", "backup.manager.delete.confirm.single": "Είστε βέβαιοι ότι θέλετε να διαγράψετε το αντίγραφο ασφαλείας \"{{fileName}}\"; Η ενέργεια αυτή δεν μπορεί να αναιρεθεί.", "backup.manager.delete.confirm.title": "Επιβεβαίωση διαγραφής", "backup.manager.delete.error": "Αποτυχία διαγραφής", "backup.manager.delete.selected": "Διαγραφή επιλεγμένων", "backup.manager.delete.success.multiple": "Τα {{count}} αντίγραφα ασφαλείας διαγράφηκαν επιτυχώς", "backup.manager.delete.success.single": "Η διαγραφή ήταν επιτυχής", "backup.manager.delete.text": "Διαγραφή", "backup.manager.fetch.error": "Αποτυχ<PERSON><PERSON> λήψης αντιγράφων ασφαλείας", "backup.manager.refresh": "Ανανέωση", "backup.manager.restore.error": "Αποτυχία επαναφορ<PERSON>ς", "backup.manager.restore.success": "Η επαναφορά ήταν επιτυχής, η εφαρμογή θα ανανεωθεί σε λίγα δευτερόλεπτα", "backup.manager.restore.text": "Επαναφορά", "backup.manager.select.files.delete": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε τα αντίγραφα ασφαλείας προς διαγραφή", "backup.manager.title": "Διαχείριση δεδομένων αντιγράφου ασφαλείας", "backup.modal.filename.placeholder": "Εισαγάγετε το όνομα του αρχείου αντιγράφου ασφαλείας", "backup.modal.title": "Αντιγραφή ασφαλείας στο WebDAV", "disableStream": {"help": "Όταν είναι ενεργοποιημένο, φορτώνει το αρχείο στη μνήμη πριν τη μεταφόρτωση, γεγονός που μπορεί να επιλύσει προβλήματα ασυμβατότητας με ορισμένες υπηρεσίες WebDAV που δεν υποστηρίζουν τη μεταφόρτωση με chunked, αλλά αυξάνει τη χρήση μνήμης.", "title": "Απενεργοποίηση μεταφόρτωσης με ροή"}, "host": "Διεύθυνση WebDAV", "host.placeholder": "http://localhost:8080", "hour_interval_one": "{{count}} ώρα", "hour_interval_other": "{{count}} ώρες", "lastSync": "Η τελευταία αντιγραφή ασφαλείας", "maxBackups": "Μέγιστο αριθμό αρχείων αντιγραφής ασφαλείας", "minute_interval_one": "{{count}} λεπτό", "minute_interval_other": "{{count}} λεπτά", "noSync": "Εκκρεμεί η επόμενη αντιγραφή ασφαλείας", "password": "Κωδικ<PERSON>ς πρόσβασης WebDAV", "path": "Διαδρομή WebDAV", "path.placeholder": "/backup", "restore.button": "Αποκατάσταση από το WebDAV", "restore.confirm.content": "Η αποκατάσταση από το WebDAV θα επιβάλλει τα σημερινά δεδομένα. Θέλετε να συνεχίσετε;", "restore.confirm.title": "Υποβεβαίωση αποκατάστασης", "restore.content": "Η αποκατάσταση από το WebDAV θα επιβάλλει τα σημερινά δεδομένα. Θέλετε να συνεχίσετε;", "restore.title": "Αποκατάσταση από το WebDAV", "syncError": "Σφάλμα στην αντιγραφή ασφαλείας", "syncStatus": "Κατάστα<PERSON>η αντιγραφής ασφαλείας", "title": "WebDAV", "user": "Όνομα χρήστη WebDAV"}, "yuque": {"check": {"button": "Έλεγχος", "empty_repo_url": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το URL του βιβλιοθηκέυματος πρώτα", "empty_token": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τον κλειδί του Yu<PERSON> πρώτα", "fail": "Απέτυχε η επαλήθευση σύνδεσης με το <PERSON>", "success": "Η επαλήθευση σύνδεσης με το <PERSON>ταν επιτυχής"}, "help": "Λήψη Token του Yusi", "repo_url": "Διεύθυνση URL του βιβλιοθικίου", "repo_url_placeholder": "https://www.yuque.com/username/xxx", "title": "Ρύθμιση <PERSON>si", "token": "Token του Yusi", "token_placeholder": "Παρακαλούμε εισάγετε το Token του Yusi"}}, "developer": {"enable_developer_mode": "Ενεργοποίηση λειτουργίας προγραμματιστή", "title": "Λειτουργία Προγραμματιστή"}, "display.assistant.title": "Ρυθμίσεις Υπηρεσίας", "display.custom.css": "Προσαρμοστική CSS", "display.custom.css.cherrycss": "Λήψη από cherrycss.com", "display.custom.css.placeholder": "/* Γρ<PERSON><PERSON><PERSON><PERSON> εδώ την προσαρμοστική CSS */", "display.navbar.position": "Θέση Γραμμής <PERSON>λοήγησης", "display.navbar.position.left": "Αριστερά", "display.navbar.position.top": "Πάνω", "display.navbar.title": "Ρυθμίσεις Γραμμής Πλοήγησης", "display.sidebar.chat.hiddenMessage": "Η υπηρεσία είναι βασική λειτουργία και δεν υποστηρίζεται η κρυμμένη εμφάνιση", "display.sidebar.disabled": "Αποκρυμμένα εικονίδια", "display.sidebar.empty": "Βάλ<PERSON><PERSON> εδώ τις λειτουργ<PERSON>ες που θέλετε να κρύψετε από την αριστερά", "display.sidebar.files.icon": "Εμφάνιση εικονιδίου αρχείων", "display.sidebar.knowledge.icon": "Εμφάνιση εικονιδίου γνώσης", "display.sidebar.minapp.icon": "Εμφάνιση εικονιδίου μικροπρογραμμάτων", "display.sidebar.painting.icon": "Εμφάνιση εικονιδίου ζωγραφικής", "display.sidebar.title": "Ρυθμίσεις πλευρικού μενού", "display.sidebar.translate.icon": "Εμφάνιση εικονιδίου μετάφρασης", "display.sidebar.visible": "Εμφανιζόμενα εικονίδια", "display.title": "Ρυθμίσεις εμφάνισης", "display.topic.title": "Ρυθμίσεις Θεμάτων", "display.zoom.title": "Ρυθμίσεις κλίμακας", "font_size.title": "Μέγ<PERSON><PERSON>ος γραμμάτων των μηνυμάτων", "general": "Γενικές ρυθμίσεις", "general.auto_check_update.title": "Αυτόματη ενημέρωση", "general.avatar.reset": "Επαναφ<PERSON><PERSON><PERSON> εικονιδίου", "general.backup.button": "Αντιγραφή ασφαλείας", "general.backup.title": "Αντιγραφή ασφαλείας και αποκατάσταση δεδομένων", "general.display.title": "Ρυθμίσεις εμφάνισης", "general.emoji_picker": "Επιλογή σμιλιών", "general.image_upload": "Φόρτωση εικόνων", "general.reset.button": "Επαναφορά", "general.reset.title": "Επαναφορ<PERSON> δεδομένων", "general.restore.button": "Αποκατάσταση", "general.spell_check": "Έλεγχος Ορθογραφίας", "general.spell_check.languages": "Γλώ<PERSON><PERSON><PERSON>ς Ελέγχου Ορθογραφίας", "general.test_plan.beta_version": "Έκδοση Βήτα (Beta)", "general.test_plan.beta_version_tooltip": "Οι λειτουργίες μπορεί να αλλάζουν ανά πάσα στιγμή, υπάρχουν πολλά σφάλματα, η ενημέρωση είναι γρήγορη", "general.test_plan.rc_version": "Έκδοση Προεπισκόπησης (RC)", "general.test_plan.rc_version_tooltip": "Κο<PERSON><PERSON><PERSON> στην επίσημη έκδοση, οι λειτουργίες είναι σταθερές, λιγότερα σφάλματα", "general.test_plan.title": "Σχέδιο Δο<PERSON>ιμής", "general.test_plan.tooltip": "Η συμμετοχή στο σχέδιο δοκιμής σας επιτρέπει να εμπειρικά τις πιο πρόσφατες λειτουργίες γρηγορότερα, <PERSON><PERSON><PERSON><PERSON> συνεπάγεται και μεγαλύτερο κίνδυνο· βεβαιωθείτε ότι έχετε δημιουργήσει αντίγραφο ασφαλείας", "general.test_plan.version_channel_not_match": "Η αλλαγή μεταξύ προεπισκόπησης και δοκιμαστικής έκδοσης θα εφαρμοστεί μετά την επόμενη επίσημη έκδοση", "general.test_plan.version_options": "Επιλογή Έκδοσης", "general.title": "Γενικές ρυθμίσεις", "general.user_name": "Όνομα χρήστη", "general.user_name.placeholder": "Εισαγάγετε όνομα χρήστη", "general.view_webdav_settings": "Προβολή ρυθμίσεων WebDAV", "hardware_acceleration": {"confirm": {"content": "Η απενεργοποίηση της υλικοποιημένης επιτάχυνσης απαιτεί επανεκκίνηση της εφαρμογής για να τεθεί σε ισχύ. Θέλετε να επανεκκινήσετε τώρα;", "title": "Απαιτεί<PERSON><PERSON><PERSON> επανεκκίνηση της εφαρμογής"}, "title": "Απενεργοποίηση επιτάχυνσης υλικού"}, "input.auto_translate_with_space": "Μετάφραση με τρεις γρήγορες πιστώσεις", "input.show_translate_confirm": "Εμφάνιση παραθύρου επιβεβαίωσης μετάφρασης", "input.target_language": "Γλώσσα προορισμού", "input.target_language.chinese": "Σινογραμματικό", "input.target_language.chinese-traditional": "Επιτυχημέν<PERSON> Σινογραμματικό", "input.target_language.english": "Αγγλικ<PERSON>", "input.target_language.japanese": "Ιαπωνικά", "input.target_language.russian": "Ρωσικά", "launch.onboot": "Αυτόματη εκκίνηση κατά την εκκίνηση του συστήματος", "launch.title": "Εκκίνηση", "launch.totray": "Εισαγωγή στην συνδρομή κατά την εκκίνηση", "mcp": {"actions": "Ενέργειες", "active": "Ενεργοποίηση", "addError": "Αποτυχία προσθήκης διακομιστή", "addServer": "Προσθήκη διακομιστή", "addServer.create": "Γρήγορη Δημιουργία", "addServer.importFrom": "Εισαγωγή από JSON", "addServer.importFrom.connectionFailed": "Αποτυχία Σύνδεσης", "addServer.importFrom.dxt": "Εισαγωγή Πακέτου DXT", "addServer.importFrom.dxtFile": "Αρχε<PERSON><PERSON> Πακέτου DXT", "addServer.importFrom.dxtHelp": "Επιλέξτε ένα αρχείο .dxt που περιέχει διακομιστή MCP", "addServer.importFrom.dxtProcessFailed": "Αποτυχία επεξεργασίας αρχείου DXT", "addServer.importFrom.invalid": "Μη έγκυρη εισαγωγή, ελέγξτε τη μορφή JSON", "addServer.importFrom.method": "Μ<PERSON>θ<PERSON><PERSON><PERSON>γωγής", "addServer.importFrom.nameExists": "Ο διακομιστής υπάρχει ήδη: {{name}}", "addServer.importFrom.noDxtFile": "Παρακ<PERSON><PERSON><PERSON> επιλέξτε ένα αρχείο DXT", "addServer.importFrom.oneServer": "Μπορεί να αποθηκευτεί μόνο μία διαμόρφωση διακομιστή MCP κάθε φορά", "addServer.importFrom.placeholder": "Επικολλήστε τη διαμόρφωση JSON του διακομιστή MCP", "addServer.importFrom.selectDxtFile": "Επιλέξτε Αρχείο DXT", "addServer.importFrom.tooltip": "Αντιγράψτε το JSON διαμόρφωσης από τη σελίδα εισαγωγής του MCP Servers (προτιμήστε\n διαμορφώσεις NPX ή UVX) και επικολλήστε το στο πεδίο εισαγωγής", "addSuccess": "Ο διακομιστής προστέθηκε επιτυχώς", "advancedSettings": "Προχωρημένες Ρυθμίσεις", "args": "Παράμετροι", "argsTooltip": "Κάθε παράμετρος σε μια γραμμή", "baseUrlTooltip": "Σύνδεσμος Απομακρυσμένης διεύθυνσης URL", "builtinServers": "Ενσωματωμένοι Διακομιστές", "command": "Εντολή", "config_description": "Ρυθμίζει το πλαίσιο συντονισμού πρωτοκόλλων διακομιστή", "customRegistryPlaceholder": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τη διεύθυνση του ιδιωτικού αποθετηρίου, π.χ.: https://npm.company.com", "deleteError": "Αποτυχία διαγραφής διακομιστή", "deleteServer": "Διαγραφή διακομιστή", "deleteServerConfirm": "Είστε σίγουρος ότι θέλετε να διαγράψετε αυτόν τον διακομιστή;", "deleteSuccess": "Ο διακομιστής διαγράφηκε επιτυχώς", "dependenciesInstall": "Εγκατάστα<PERSON>η εξαρτήσεων", "dependenciesInstalling": "Βράζουν οι εξαρτήσεις...", "description": "Περιγραφή", "disable": "Να μην χρησιμοποιείται διακομιστής MCP", "disable.description": "Να μην ενεργοποιείται η λειτουργία υπηρεσίας MCP", "duplicateName": "Υπάρχει ήδη ένας διακομιστής με αυτό το όνομα", "editJson": "Επεξεργασία JSON", "editMcpJson": "Επεξεργασία ρύθμισης MCP", "editServer": "Επεξεργασία διακομιστή", "env": "Περιβαλλοντικές μεταβλητές", "envTooltip": "Μορφή: KEY=value, κάθε μια σε μια γραμμή", "errors": {"32000": "Η εκκίνηση του MCP απέτυχε. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε αν όλες οι παράμετροι έχουν συμπληρωθεί σύμφωνα με τον οδηγό.", "toolNotFound": "Δεν βρέθηκε το εργαλείο {{name}}"}, "findMore": "Περισσότεροι διακομιστές MCP", "headers": "Κεφαλίδες", "headersTooltip": "Προσαρμοσμένες κεφαλίδες HTTP αιτήσεων", "inMemory": "Σε Μνήμη", "install": "Εγκατάσταση", "installError": "Αποτυχ<PERSON><PERSON> εγκατάστα<PERSON>ης εξαρτήσεων", "installHelp": "Λή<PERSON>η βοήθειας εγκατάστασης", "installSuccess": "Η εγκατάσταση των εξαρτήσεων ολοκληρώθηκε επιτυχώς", "jsonFormatError": "Σφάλμα στη μορφοποίηση JSON", "jsonModeHint": "Επεξεργα<PERSON><PERSON><PERSON> της εκφώνησης JSON του διακομιστή MCP. Πα<PERSON>α<PERSON><PERSON><PERSON><PERSON> εξασφαλίστε ότι το μορφοποίηση είναι σωστό πριν από την αποθήκευση.", "jsonSaveError": "Αποτυχία αποθήκευσης της διαμορφωτικής ρύθμισης JSON", "jsonSaveSuccess": "Η διαμορφωτική ρύθμιση JSON αποθηκεύτηκε επιτυχώς", "logoUrl": "URL Λογότυπου", "missingDependencies": "Απο缺失, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εγκαταστήστε το για να συνεχίσετε", "more": {"awesome": "Επιλεγ<PERSON><PERSON><PERSON>ος κατάλογος διακομιστών MCP", "composio": "Εργαλε<PERSON><PERSON> ανάπτυ<PERSON>ης Composio MCP", "glama": "Κατά<PERSON><PERSON><PERSON><PERSON> διακομιστών Glama MCP", "higress": "Διακομιστής MCP Higress", "mcpso": "Πλατφόρμα ανακάλυψης διακομιστών MCP", "modelscope": "Διακομιστής MCP κοινότητας ModelScope", "official": "Επίσημη συλλογή διακομιστών MCP", "pulsemcp": "Διακομιστής Pulse MCP", "smithery": "Εργαλεί<PERSON> Smithery MCP"}, "name": "Όνομα", "newServer": "Διακομιστής MCP", "noDescriptionAvailable": "Δεν υπάρχει διαθέσιμη περιγραφή", "noServers": "Δεν έχουν ρυθμιστεί διακομιστές", "not_support": "Το μοντέλο δεν υποστηρίζεται", "npx_list": {"actions": "Ενέργειες", "description": "Περιγραφή", "no_packages": "Δεν βρέθηκαν πακέτα", "npm": "NPM", "package_name": "Όνομα πακέτου", "scope_placeholder": "Εισαγάγετε το σκοπό του npm (π.χ. @your-org)", "scope_required": "Παρα<PERSON><PERSON><PERSON><PERSON> εισαγάγετε το σκοπό του npm", "search": "Αναζήτηση", "search_error": "Η αναζήτηση απέτυχε", "usage": "<PERSON>ρ<PERSON><PERSON><PERSON>", "version": "Έκδοση"}, "prompts": {"arguments": "Ορίσματα", "availablePrompts": "Διαθέσιμες Υποδείξεις", "genericError": "Σφάλμα κατά τη λήψη της υπόδειξης", "loadError": "Αποτυχία λήψης υπόδειξης", "noPromptsAvailable": "Δεν υπάρχουν διαθέσιμες υποδείξεις", "requiredField": "Υποχρεωτικό πεδίο"}, "provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "providerPlaceholder": "Όνομα παρόχου", "providerUrl": "URL Παρόχου", "registry": "Πηγή Διαχείρισης πακέτων", "registryDefault": "Προεπιλεγμένη", "registryTooltip": "Επιλέξτε την πηγή για την εγκατάσταση πακέτων, για να αντιμετωπιστούν προβλήματα δικτύου από την προεπιλεγμένη πηγή.", "requiresConfig": "Απαιτείτ<PERSON><PERSON> Διαμόρφωση", "resources": {"availableResources": "Διαθέσιμοι πόροι", "blob": "Δυαδικά δεδομένα", "blobInvisible": "Αόρατα δυαδικά δεδομένα", "mimeType": "Τύπος MIME", "noResourcesAvailable": "Δεν υπάρχουν διαθέσιμοι πόροι", "size": "Μέγεθος", "text": "Κείμενο", "uri": "URI"}, "searchNpx": "Αναζήτηση MCP", "serverPlural": "Διακομιστές", "serverSingular": "Διακομιστής", "sse": "Συμβάντα Αποστολής από τον Διακομιστή (sse)", "startError": "Εκκίνηση Απέτυχε", "stdio": "Πρότυπη Είσοδος/Έξοδος (stdio)", "streamableHttp": "Ρέουσα μεταφορά HTTP (streamableHttp)", "sync": {"button": "Συγχρονισμός", "discoverMcpServers": "Ανακάλυψη MCP Διακομιστών", "discoverMcpServersDescription": "Πρόσβαση στην πλατφόρμα για ανακάλυψη διαθέσιμων MCP διακομιστών", "error": "Σφάλμα κατά τον συγχρονισμό MCP διακομιστή", "getToken": "Λήψη API Τοκεν", "getTokenDescription": "Λήψη ενός προσωπικού API τοκεν από τον λογαριασμό σας", "noServersAvailable": "Δεν υπάρχουν διαθέσιμοι MCP διακομιστές", "selectProvider": "Επιλέξτε Πάροχο:", "setToken": "Εισαγάγετε το τοκεν σας", "success": "Ο συγχρονι<PERSON><PERSON><PERSON>ς MCP διακομιστή ολοκληρώθηκε επιτυχώς", "title": "Συγχρονισμ<PERSON>ς Διακομιστή", "tokenPlaceholder": "Εισάγετε το API τοκεν εδώ", "tokenRequired": "Απαιτείται API Τοκεν", "unauthorized": "Δεν εξουσιοδοτήθηκε ο συγχρονισμός"}, "system": "Σύστημα", "tabs": {"description": "Περιγραφή", "general": "Γενικά", "prompts": "Ερωτήματα", "resources": "Πόροι", "tools": "Εργαλεία"}, "tags": "Ετικέτες", "tagsPlaceholder": "Εισάγετε ετικέτες", "timeout": "Τερματισ<PERSON><PERSON>ς λόγω αδράνειας", "timeoutTooltip": "Ο χρόνος λήξης αιτήσεων για αυτόν τον διακομιστή (σε δευτερόλεπτα), προεπιλεγμένος είναι 60 δευτερόλεπτα", "title": "Διακ<PERSON><PERSON>ιστές MCP", "tools": {"autoApprove": "Αυτόματη έγκριση", "autoApprove.tooltip.confirm": "Να εκτελεστεί αυτό το εργαλείο MCP;", "autoApprove.tooltip.disabled": "Απαιτε<PERSON><PERSON><PERSON><PERSON> χειροκίνητη έγκριση πριν την εκτέλεση του εργαλείου", "autoApprove.tooltip.enabled": "Το εργαλείο θα εκτελείται αυτόματα χωρίς έγκριση", "autoApprove.tooltip.howToEnable": "Η αυτόματη έγκριση είναι διαθέσιμη μόνο αφού ενεργοποιηθεί το εργαλείο", "availableTools": "Διαθέσιμα Εργαλεία", "enable": "Ενεργοποίηση εργαλείου", "inputSchema": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSchema.enum.allowedValues": "Επιτρεπόμενες τιμές", "loadError": "Αποτυχ<PERSON>α φόρτωσης εργαλείων", "noToolsAvailable": "Δεν υπάρχουν διαθέσιμα εργαλεία", "run": "Εκτέλεση"}, "type": "Τύπος", "types": {"inMemory": "Ενσωματωμένη", "sse": "SSE", "stdio": "STDIO", "streamableHttp": "Ροή"}, "updateError": "Αποτυχ<PERSON>α ενημέρωσης διακομιστή", "updateSuccess": "Ο διακομιστής ενημερώθηκε επιτυχώς", "url": "URL", "user": "<PERSON>ρή<PERSON><PERSON><PERSON>ς"}, "messages.divider": "Διαχωριστική γραμμή μηνυμάτων", "messages.divider.tooltip": "Δεν ισχύει για μηνύματα με στυλ φυσαλίδας", "messages.grid_columns": "Αριθμός στήλων γριλ μηνυμάτων", "messages.grid_popover_trigger": "Καταγ<PERSON>α<PERSON><PERSON> στοιχείων στο grid", "messages.grid_popover_trigger.click": "Εμφάνιση κλικ", "messages.grid_popover_trigger.hover": "Εμφάνιση επιστροφής", "messages.input.enable_delete_model": "Ενεργοποίηση διαγραφής μοντέλων/επισυναπτόμενων αρχείων με το πλήκτρο διαγραφής", "messages.input.enable_quick_triggers": "Ενεργοποίηση των '/' και '@' για γρήγορη πρόσβαση σε μενού", "messages.input.paste_long_text_as_file": "Επικόλληση μεγάλου κειμένου ως αρχείο", "messages.input.paste_long_text_threshold": "Όριο μεγάλου κειμένου", "messages.input.send_shortcuts": "Συντάγματα αποστολής", "messages.input.show_estimated_tokens": "Εμφάνιση εκτιμώμενου αριθμού token", "messages.input.title": "Ρυθμίσεις εισαγωγής", "messages.markdown_rendering_input_message": "Markdown Rendering Input Message", "messages.math_engine": "Μηχανική μαθηματικών εξισώσεων", "messages.math_engine.none": "Κανένα", "messages.metrics": "<PERSON>ρ<PERSON><PERSON><PERSON> πρώτου χαρακτήρα {{time_first_token_millsec}}ms | {{token_speed}} tokens ανά δευτερόλεπτο", "messages.model.title": "Ρυθμίσεις μοντέλου", "messages.navigation": "Κουμπιά πλοήγησης συζητήσεων", "messages.navigation.anchor": "<PERSON><PERSON> συζητήσεων", "messages.navigation.buttons": "Πάνω και κάτω κουμπιά", "messages.navigation.none": "<PERSON><PERSON><PERSON><PERSON><PERSON> εμφάνιση", "messages.prompt": "Λήμμα προτροπής", "messages.title": "Ρυθμίσεις μηνυμάτων", "messages.use_serif_font": "Χρήση μορ<PERSON>ή<PERSON>", "mineru.api_key": "Το MinerU παρέχει δωρεάν χρήση 500 σελίδων ημερησίως, δεν χρειάζεται να συμπληρώσετε κλειδί.", "miniapps": {"cache_change_notice": "Η αλλαγή θα τεθεί σε ισχύ αφού το πλήθος των ανοιχτών μικροπρογραμμάτων φτάσει τη ρυθμισμένη τιμή", "cache_description": "Ορίστε τον μέγιστο αριθμό των μικροπρογραμμάτων που μπορούν να είναι ενεργά ταυτόχρονα", "cache_settings": "Ρυθμίσεις Προσωρινής Μνήμης", "cache_title": "Ποσότητα Προσωρινής Μνήμης Μικροπρογράμματος", "custom": {"conflicting_ids": "Υπάρχει σύγκρουση με τα προεπιλεγμένα ID της εφαρμογής: {{ids}}", "duplicate_ids": "Εντοπίστηκαν διπλότυπα ID: {{ids}}", "edit_description": "Επεξεργαστείτε τη διαμόρφωση της προσαρμοσμένης σας εφαρμογής εδώ. Κάθε εφαρμογή πρέπει να περιλαμβάνει τα πεδία id, name, url και logo.", "edit_title": "Επεξεργασία Προσαρμοσμένης Εφαρμογής", "id": "ID", "id_error": "Το ID είναι υποχρεωτικό πεδίο.", "id_placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το ID", "logo": "Logo", "logo_file": "Μεταφόρτωση Logo Αρχείου", "logo_upload_button": "Μεταφόρτωση", "logo_upload_error": "Αποτυχία μεταφόρτωσης του Logo.", "logo_upload_label": "Μεταφόρτωση Logo", "logo_upload_success": "Το Logo μεταφορτώθηκε επιτυχώς.", "logo_url": "Logo URL", "logo_url_label": "Logo URL", "logo_url_placeholder": "Παρακ<PERSON><PERSON><PERSON> εισάγετε το Logo URL", "name": "Όνομα", "name_error": "Το Όνομα είναι υποχρεωτικό πεδίο.", "name_placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το όνομα", "placeholder": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τη διαμόρφωση της προσαρμοσμένης εφαρμογής (Μορφή JSON)", "remove_error": "Αποτυχία διαγραφής της προσαρμοσμένης εφαρμογής.", "remove_success": "Η προσαρμοσμένη εφαρμογή διαγράφηκε επιτυχώς.", "save": "Αποθήκευση", "save_error": "Αποτυχία αποθήκευσης της προσαρμοσμένης εφαρμογής.", "save_success": "Η προσαρμοσμένη εφαρμογή αποθηκεύτηκε επιτυχώς.", "title": "Προσαρμοσμένη Εφαρμογή", "url": "URL", "url_error": "Το URL είναι υποχρεωτικό πεδίο.", "url_placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το URL"}, "disabled": "Απόκρυψη μικροπρογράμματος", "display_title": "Ρυθμίσεις Εμφάνισης Μικροπρογράμματος", "empty": "Σύρετε το μικροπρόγραμμα που θέλετε να αποκρύψετε από την αριστερή πλευρά σε αυτήν την περιοχή", "open_link_external": {"title": "Άνοιγμα νέου παραθύρου σύνδεσης στον περιηγητή"}, "reset_tooltip": "Επανα<PERSON><PERSON><PERSON><PERSON> στις προεπιλεγμένες τιμές", "sidebar_description": "Καθορίστε εάν το ενεργό μικροπρόγραμμα θα εμφανίζεται στην πλευρική γραμμή", "sidebar_title": "Ρυθμίσεις Εμφάνισης Ενεργού Μικροπρογράμματος στην Πλευρική Γραμμή", "title": "Ρυθμίσεις Μικροπρογράμματος", "visible": "Εμφανιζόμενα μικροπρογράμματα"}, "model": "Πρόεδρος Υπηρεσίας", "models.add.add_model": "Προσθήκη μοντέλου", "models.add.batch_add_models": "Προσθήκη Μοντέλων σε Μαζική Βάση", "models.add.endpoint_type": "Τύ<PERSON><PERSON> Endpoint", "models.add.endpoint_type.placeholder": "Επιλέξτε τύπο endpoint", "models.add.endpoint_type.required": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε τύπο endpoint", "models.add.endpoint_type.tooltip": "Επιλέξτε τη μορφή τύπου endpoint του API", "models.add.group_name": "Όνομα ομάδας", "models.add.group_name.placeholder": "Για παράδειγμα ChatGPT", "models.add.group_name.tooltip": "Για παράδειγμα ChatGPT", "models.add.model_id": "ID μοντέλου", "models.add.model_id.placeholder": "Απαραίτητο για παράδειγμα gpt-3.5-turbo", "models.add.model_id.select.placeholder": "Επιλέξτε μοντέλο", "models.add.model_id.tooltip": "Για παράδειγμα gpt-3.5-turbo", "models.add.model_name": "Όνομα μοντέλου", "models.add.model_name.placeholder": "Για παράδειγμα GPT-3.5", "models.add.model_name.tooltip": "Για παράδειγμα GPT-4", "models.api_key": "Κλειδί API", "models.base_url": "Βασικό URL", "models.check.all": "Όλα", "models.check.all_models_passed": "Όλα τα μοντέλα περάσαν ενεργειακά", "models.check.button_caption": "Ελε<PERSON><PERSON><PERSON> υγείας", "models.check.disabled": "Απενεργοποίηση", "models.check.disclaimer": "Η υγειονομική ελέγχου απαιτ<PERSON><PERSON> αποστολή αιτήματος, χρησιμοποιήστε με προσοχή. Τα μοντέλα που χρεώνονται ανά αίτημα μπορεί να προκαλέσουν επιπλέον έξοδα, τα οποία αναλαμβάνετε εσείς", "models.check.enable_concurrent": "Επιτρέπει τη συγχρονη ελεγχος", "models.check.enabled": "Ενεργοποίηση", "models.check.failed": "Αποτυχία", "models.check.keys_status_count": "Επιτυχημένοι: {{count_passed}} κλειδιά, αποτυχημένοι: {{count_failed}} κλειδιά", "models.check.model_status_failed": "{{count}} μοντέλα είναι εντελώς απρόσιτα", "models.check.model_status_partial": "Απ<PERSON> αυτά, {{count}} μοντέλα είναι απρόσιτα με ορισμένα κλειδιά", "models.check.model_status_passed": "{{count}} μοντ<PERSON><PERSON><PERSON> πέρασαν τον έλεγχο υγείας", "models.check.model_status_summary": "{{provider}}: {{count_passed}} μοντέλα ελέγχθηκαν επιτυχώς (από τα οποία {{count_partial}} μοντέλα δεν είναι προσβάσιμα με ορισμένα κλειδιά), {{count_failed}} μοντέλα είναι εντελώς απρόσβαστα.", "models.check.no_api_keys": "Δεν βρέθηκαν API κλειδιά. Παρακαλούμε πρώτα προσθέστε κλειδιά API.", "models.check.passed": "Επιτυχία", "models.check.select_api_key": "Επιλέξτε το API key που θέλετε να χρησιμοποιήσετε:", "models.check.single": "Μόνο", "models.check.start": "Έναρξη", "models.check.title": "Ελε<PERSON><PERSON>ος υγείας μοντέλου", "models.check.use_all_keys": "Χρή<PERSON>η όλων των κλειδιών", "models.default_assistant_model": "Πρόεδρος Υπηρεσίας προεπιλεγμένου μοντέλου", "models.default_assistant_model_description": "Το μοντέλο που χρησιμοποιείτα<PERSON> όταν δημιουργείτε νέο υπάλληλο. Αν το υπάλληλο δεν έχει επιλεγμένο ένα μοντέλο, τότε θα χρησιμοποιεί αυτό το μοντέλο.", "models.empty": "Δεν υπάρχουν μοντέλα", "models.enable_topic_naming": "Αυτόματη αναδόμηση θεμάτων", "models.manage.add_listed": "Προσθήκη μοντέλων από τη λίστα", "models.manage.add_whole_group": "Προσθήκη ολόκληρης ομάδας", "models.manage.remove_listed": "Αφαίρεση μοντέλων από τη λίστα", "models.manage.remove_model": "Αφαίρεση Μοντέλου", "models.manage.remove_whole_group": "Αφαίρεση ολόκληρης ομάδας", "models.provider_id": "Αναγνω<PERSON>ιστι<PERSON><PERSON> Παρόχου", "models.provider_key_add_confirm": "Θέλετε να προσθέσετε κλειδί API για τον {{provider}};", "models.provider_key_add_failed_by_empty_data": "Η προσθήκη κλειδιού API παρόχου απέτυχε, τα δεδομένα είναι κενά", "models.provider_key_add_failed_by_invalid_data": "Η προσθήκη κλειδιού API παρόχου απέτυχε, λάθος μορφή δεδομένων", "models.provider_key_added": "Επιτυχής προσθήκη κλειδιού API για τον {{provider}}", "models.provider_key_already_exists": "Το κλειδί API για τον {{provider}} υπάρχει ήδη, δεν θα προστεθεί ξανά", "models.provider_key_confirm_title": "Προσθήκη κλειδιού API για τον {{provider}}", "models.provider_key_no_change": "Το κλειδί API του {{provider}} δεν άλλαξε", "models.provider_key_overridden": "Επιτυχής ενημέρωση του κλειδιού API για τον {{provider}}", "models.provider_key_override_confirm": "Το κλειδί API για τον {{provider}} υπάρχει ήδη, θέλετε να το αντικαταστήσετε;", "models.provider_name": "Όνομα Παρόχου", "models.quick_assistant_default_tag": "Προεπιλογή", "models.quick_assistant_model": "Μοντ<PERSON><PERSON><PERSON>ρήγορου Βοηθού", "models.quick_assistant_model_description": "Προεπιλεγ<PERSON><PERSON>ν<PERSON> μοντέλο που χρησιμοποιείται από το Γρήγορο Βοηθό", "models.quick_assistant_selection": "Επιλογή Βοηθού", "models.topic_naming_model": "Μον<PERSON><PERSON><PERSON><PERSON> αναδόμησης θεμάτων", "models.topic_naming_model_description": "Το μοντέλο που χρησιμοποιεί<PERSON><PERSON><PERSON> όταν αυτόματα ονομάζεται ένα νέο θέμα", "models.topic_naming_model_setting_title": "Ρυθμίσεις Μοντέλου Αναδόμησης Θεμάτων", "models.topic_naming_prompt": "Προσδιορισμ<PERSON>ς προκαθορισμένου θέματος", "models.translate_model": "Μοντ<PERSON><PERSON><PERSON> μετάφρασης", "models.translate_model_description": "Το μοντέλο που χρησιμοποιείται για τη μετάφραση", "models.translate_model_prompt_message": "Εισάγετε την προσδιορισμένη προειδοποίηση μετάφρασης", "models.translate_model_prompt_title": "Προσδιορισμ<PERSON>ς προκαθορισμένου θέματος μετάφρασης", "models.use_assistant": "Χρή<PERSON>η Βοηθού", "models.use_model": "Προεπιλεγ<PERSON><PERSON><PERSON><PERSON>λο", "moresetting": "Περισσότερες ρυθμίσεις", "moresetting.check.confirm": "Επιβεβαίωση επιλογής", "moresetting.check.warn": "Παρακαλούμε επιλέξτε με προσοχή αυτή την επιλογή, μια λάθος επιλογή μπορεί να εμποδίσει την σωστή λειτουργία του μοντέλου!!", "moresetting.warn": "Χρησιμοποιείται κίνδυνος", "notification": {"assistant": "Μήνυμα βοηθού", "backup": "Δημιουργ<PERSON><PERSON> αντιγράφου ασφαλείας", "knowledge_embed": "Βάση γνώσης", "title": "Ρυθμίσεις ειδοποιήσεων"}, "openai": {"service_tier.auto": "Αυτόματο", "service_tier.default": "Προεπιλογή", "service_tier.flex": "Εύκαμπτο", "service_tier.tip": "Καθορίστε το επίπεδο καθυστέρησης που χρησιμοποιείται για την επεξεργασία των αιτημάτων", "service_tier.title": "Επίπεδο υπηρεσίας", "summary_text_mode.auto": "Αυτόματο", "summary_text_mode.concise": "Σύντομο", "summary_text_mode.detailed": "Λεπτομερές", "summary_text_mode.off": "Απενεργοποιημένο", "summary_text_mode.tip": "Περίληψη συλλογισμού που εκτελείται από το μοντέλο", "summary_text_mode.title": "Λειτουργ<PERSON>α περίληψης", "title": "Ρυθμίσεις OpenAI"}, "privacy": {"enable_privacy_mode": "Αποστο<PERSON><PERSON> ανώνυμων αναφ<PERSON>ρών σφαλμάτων και στατιστικών δεδομένων", "title": "Ρυθμίσεις Απορρήτου"}, "provider": {"add.name": "Όνομα παρόχου", "add.name.placeholder": "π.χ. OpenAI", "add.title": "Προσθήκη παρόχου", "add.type": "Τύ<PERSON><PERSON> παρόχου", "api.key.check.latency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "api.key.error.duplicate": "Το κλειδί API υπάρχει ήδη", "api.key.error.empty": "Το κλειδί API δεν μπορεί να είναι κενό", "api.key.list.open": "Άνοιγμα διεπαφής διαχείρισης", "api.key.list.title": "Διαχείριση κλειδιών API", "api.key.new_key.placeholder": "Εισαγω<PERSON><PERSON> ενός ή περισσότερων κλειδιών", "api.url.preview": "Προεπισκόπηση: {{url}}", "api.url.reset": "Επαναφορά", "api.url.tip": "/τέλος αγνόηση v1 έκδοσης, #τέλος ενδεχόμενη χρήση της εισαγωγής διευθύνσεως", "api_host": "Διεύθυνση API", "api_key": "Κλειδί API", "api_key.tip": "Χω<PERSON>ιστά με κόμμα περισσότερα κλειδιά API", "api_version": "Έκδοση API", "azure.apiversion.tip": "Η έκδοση του API για Azure OpenAI. Αν θέλετε να χρησιμοποιήσετε το Response API, εισάγετε μια προεπισκόπηση έκδοσης", "basic_auth": "Πιστοποίηση HTTP", "basic_auth.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "basic_auth.tip": "Ισχύει για περιπτώσεις που τοποθετούνται σε διακομιστή (δείτε την τεκμηρίωση). Υποστηρίζεται μόνο το σχήμα Basic (RFC7617).", "basic_auth.user_name": "Όνομα χρήστη", "basic_auth.user_name.tip": "Αφήστε κενό για να απενεργοποιήσετε", "bills": "Λογαριασμοί", "charge": "Κατέβασμα", "check": "Έλεγχος", "check_all_keys": "Έλεγχος όλων των κλειδιών", "check_multiple_keys": "Έλεγχος πολλαπλών κλειδιών API", "copilot": {"auth_failed": "Η επιβεβαίωση του <PERSON><PERSON><PERSON> Copilot απέτυχε", "auth_success": "Η επιβεβαίωση του <PERSON><PERSON><PERSON>αν επιτυχής", "auth_success_title": "Η επιβεβαίωση ήταν επιτυχής", "code_copied": "Ο κωδικός εξουσιοδότησης αντιγράφηκε αυτόματα στο πρόχειρο", "code_failed": "Η λήψη του Device Code απέτυχε, παρα<PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "code_generated_desc": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> αντιγράψτε το Device Code στον παρακάτω σύνδεσμο περιηγητή", "code_generated_title": "Λήψη Device Code", "connect": "Σύνδεση με το <PERSON>", "custom_headers": "Προσαρμοσμένες κεφαλίδες αιτήματος", "description": "Ο λογαριασμός σας στο <PERSON>ub χρειάζεται να εγγραφεί για να χρησιμοποιήσει το Copilot", "description_detail": "Το GitHub Copilot είναι ένας βοηθός κώδικα με βάση την τεχνητή νοημοσύνη, για τον οποίο απαιτείται μια έγκυρη συνδρομή GitHub Copilot για να χρησιμοποιηθεί", "expand": "Επεκτάση", "headers_description": "Προσαρμοσμένες κεφαλίδες αιτήματος (σε JSON μορφή)", "invalid_json": "Λάθος σύνταξη JSON", "login": "Σύνδεση με το <PERSON>", "logout": "Αποσύνδεση από το <PERSON>", "logout_failed": "Η αποσύνδεση απέτυχε, παρ<PERSON><PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "logout_success": "Έγινε επιτυχής η αποσύνδεση", "model_setting": "Ρυθμίσεις μοντέλου", "open_verification_first": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> κάντε κλικ στον παραπ<PERSON>νω σύνδεσμο για να επισκεφτείτε τη σελίδα επιβεβαίωσης", "open_verification_page": "Άνοιγμα σελίδας εξουσιοδότησης", "rate_limit": "Όριο ρυθμού", "start_auth": "Έναρξη εξουσιοδότησης", "step_authorize": "Άνοιγμα σελίδας εξουσιοδότησης", "step_authorize_desc": "Ολοκληρώστε την εξουσιοδότηση στο <PERSON>ub", "step_authorize_detail": "Κάντε κλικ στο κάτω κουμπί για να ανοίξετε τη σελίδα εξουσιοδότησης του GitHub και στη συνέχεια εισαγάγετε τον αντιγραμμένο κωδικό εξουσιοδότησης", "step_connect": "Ολοκλήρωση σύνδεσης", "step_connect_desc": "Επιβεβαιώστε τη σύνδεση με το GitHub", "step_connect_detail": "Αφού ολοκληρώσετε την εξουσιοδότηση στη σελίδα του G<PERSON><PERSON><PERSON>, κάντε κλικ σε αυτό το κουμπί για να ολοκληρώσετε τη σύνδεση", "step_copy_code": "Αντιγρα<PERSON><PERSON> κωδικού εξουσιοδότησης", "step_copy_code_desc": "Αντιγραφ<PERSON> κωδικού εξουσιοδότησης συσκευής", "step_copy_code_detail": "Ο κωδικός εξουσιοδότησης αντιγράφηκε αυτόματα, μπορείτε επίσης να τον αντιγράψετε χειροκίνητα", "step_get_code": "<PERSON>ήψη κωδικού εξουσιοδότησης", "step_get_code_desc": "Δημιουρ<PERSON><PERSON><PERSON> κωδικού εξουσιοδότησης συσκευής"}, "delete.content": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτόν τον παροχό;", "delete.title": "Διαγρα<PERSON><PERSON> παρόχου", "dmxapi": {"select_platform": "Επιλέξτε πλατφόρμα"}, "docs_check": "Άνοιγμα", "docs_more_details": "Λάβετε περισσότερες λεπτομέρειες", "get_api_key": "Κάντε κλικ εδώ για να πάρετε κλειδί", "is_not_support_array_content": "Ενεργοποίηση συμβατικού μοντέλου", "no_models_for_check": "Δεν υπάρχουν μοντέλα για έλεγχο (π.χ. μοντέλα συνομιλίας)", "not_checked": "Δεν ελέγχεται", "notes": {"markdown_editor_default_value": "Περιοχή Προεπισκόπησης", "placeholder": "Εισάγετε περιεχόμενο σε μορφή Markdown...", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ου"}, "oauth": {"button": "Σύνδεση με λογαριασμό {{provider}}", "description": "Η υπηρεσία παρέχεται από την ιστοσελίδα {{provider}}", "official_website": "Επίσημη ιστοσελίδα"}, "openai": {"alert": "Ο πάροχος OpenAI δεν υποστηρίζει πλέον την παλιά μέθοδο κλήσης, παρα<PERSON><PERSON><PERSON><PERSON> δημιουργήστε έναν νέο πάροχο API αν χρησιμοποιείτε τρίτους"}, "remove_duplicate_keys": "Αφαίρεση Επαναλαμβανόμενων Κλειδιών", "remove_invalid_keys": "Διαγρα<PERSON><PERSON> Ακυρωμένων Κλειδιών", "search": "Αναζήτηση πλατφόρμας μονάδων...", "search_placeholder": "Αναζήτηση ID ή ονόματος μονάδας", "title": "Υπηρεσία μονάδων", "vertex_ai": {"documentation": "Δείτε την επίσημη τεκμηρίωση για περισσότερες λεπτομέρειες ρύθμισης:", "learn_more": "Μάθετε περισσότερα", "location": "Περιοχή", "location_help": "Η περιοχή της υπηρεσίας Vertex AI, π.χ. us-central1", "project_id": "Αναγνωριστικό έργου", "project_id_help": "Το αναγνωριστικό έργου Google Cloud", "project_id_placeholder": "your-google-cloud-project-id", "service_account": {"auth_success": "Η πιστοποίηση λογαριασμού υπηρεσίας ήταν επιτυχής", "client_email": "<PERSON><PERSON>", "client_email_help": "Το πεδίο client_email από το αρχείο κλειδιού JSON που κατεβάσατε από το Google Cloud Console", "client_email_placeholder": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε το email πελάτη του λογαριασμού υπηρεσίας", "description": "Επαλήθευση με λογαριασμό υπηρεσίας, κατάλληλο για περιβάλλοντα όπου δεν είναι διαθέσιμο το ADC", "incomplete_config": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> συμπληρώστε πρώτα πλήρως τις πληροφορίες του λογαριασμού υπηρεσίας", "private_key": "Ιδιωτικό κλειδί", "private_key_help": "Το πεδίο private_key από το αρχείο κλειδιού JSON που κατεβάσατε από το Google Cloud Console", "private_key_placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το ιδιωτικ<PERSON> κλειδί του λογαριασμού υπηρεσίας", "title": "Διαμόρφωση λογαριασμού υπηρεσίας"}}}, "proxy": {"address": "Διεύθυνση διαμεσολάβησης", "mode": {"custom": "προσαρμοσμένη προξενική", "none": "χωρίς πρόξενο", "system": "συστηματική προξενική", "title": "κλίμακα προξενικής"}}, "quickAssistant": {"click_tray_to_show": "Επιλέξτε την εικόνα στο πίνακα για να ενεργοποιήσετε", "enable_quick_assistant": "Ενεργοποίηση γρήγορου βοηθού", "read_clipboard_at_startup": "Αναγνωρίζει το πρόχειρο κατά την εκκίνηση", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> βοηθός", "use_shortcut_to_show": "Κάντε δεξ<PERSON><PERSON><PERSON> κλικ στην εικόνα του πίνακα ή χρησιμοποιήστε την συντομεύση για να ενεργοποιήσετε"}, "quickPanel": {"back": "Πίσω", "close": "Κλείσιμο", "confirm": "Επιβεβαίωση", "forward": "Μπρ<PERSON>", "multiple": "Πολλαπλή επιλογή", "page": "Σελίδα", "select": "Επιλογή", "title": "Γρήγορη Πρόσβαση"}, "quickPhrase": {"add": "Προσθήκη Φράσης", "assistant": "Φρά<PERSON><PERSON><PERSON><PERSON> Βοηθού", "contentLabel": "Περιεχόμενο", "contentPlaceholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε περιεχόμενο φράσης. Υποστηρίζεται η χρήση μεταβλητών, και στη συνέχεια πατήστε Tab για να μεταβείτε γρήγορα στη μεταβλητή και να την τροποποιήσετε. Για παράδειγμα: \\\\n Βοήθησέ με να σχεδιάσω μια διαδρομή από το ${from} στο ${to}, και στη συνέχεια να τη στείλεις στο ${email}.", "delete": "Διαγρα<PERSON>ή Φράσης", "deleteConfirm": "Η διαγραφή της φράσης δεν μπορεί να αναιρεθεί. Θέλετε να συνεχίσετε;", "edit": "Επεξεργασία Φράσης", "global": "Κοιν<PERSON>ς Φράσεις", "locationLabel": "Προσθήκη Τοποθεσίας", "title": "Γρήγο<PERSON><PERSON><PERSON>ς", "titleLabel": "Τίτλος", "titlePlaceholder": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τίτλο φράσης"}, "shortcuts": {"action": "Ενέργεια", "clear_shortcut": "Καθαρισμ<PERSON>ς συντομού πλήκτρου", "clear_topic": "Άδειασμα μηνυμάτων", "copy_last_message": "Αντιγραφή του τελευταίου μηνύματος", "exit_fullscreen": "Έξοδος από πλήρη οθόνη", "key": "Πλήκτρο", "mini_window": "Συντομεύστε επιχειρηματικά", "new_topic": "Νέο θέμα", "press_shortcut": "Πάτησε το συντομού πλήκτρου", "reset_defaults": "Επαναφ<PERSON><PERSON><PERSON> στα προεπιλεγμένα συντομού πλήκτρα", "reset_defaults_confirm": "Θέλετε να επαναφέρετε όλα τα συντομού πλήκτρα στις προεπιλεγμένες τιμές;", "reset_to_default": "Επανα<PERSON><PERSON><PERSON><PERSON> στις προεπιλεγμένες", "search_message": "Αναζήτηση μηνυμάτων", "search_message_in_chat": "Αναζήτηση μηνύματος στην τρέχουσα συνομιλία", "selection_assistant_select_text": "Βο<PERSON><PERSON><PERSON><PERSON> επιλογής κειμένου: επιλογή λέξης", "selection_assistant_toggle": "Εναλλαγή βοηθού επιλογής κειμένου", "show_app": "Εμφάνιση εφαρμογής", "show_settings": "Άνοιγμα των ρυθμίσεων", "title": "Συντομοί δρομολόγια", "toggle_new_context": "Άδειασμα σενάριων", "toggle_show_assistants": "Εναλλαγή εμφάνισης βοηθών", "toggle_show_topics": "Εναλλαγή εμφάνισης θεμάτων", "zoom_in": "Μεγέθυνση εμφάνισης", "zoom_out": "Σμικρύνση εμφάνισης", "zoom_reset": "Επανα<PERSON><PERSON><PERSON><PERSON> εμφάνισης"}, "theme.color_primary": "Κ<PERSON><PERSON><PERSON><PERSON>ρώ<PERSON> Θέματος", "theme.dark": "Σκοτεινό", "theme.light": "Φωτεινό", "theme.system": "Σύστημα", "theme.title": "Θέμα", "theme.window.style.opaque": "Μη διαφανή παράθυρα", "theme.window.style.title": "Στυλ παραθύρων", "theme.window.style.transparent": "Διαφανή παράθυρα", "title": "Ρυθμίσεις", "tool": {"ocr": {"mac_system_ocr_options": {"min_confidence": "Ελάχιστη βαθμίδα εμπιστοσύνης", "mode": {"accurate": "Ακριβής", "fast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Μοτίβ<PERSON> Αναγνώρισης"}}, "provider": "Πάροχος O<PERSON>", "provider_placeholder": "Επιλέξτε έναν πάροχο OCR", "title": "Αναγνώριση κειμένου OCR"}, "preprocess": {"provider": "Π<PERSON><PERSON><PERSON><PERSON><PERSON> προεπεξεργασίας εγγράφων", "provider_placeholder": "Επιλέξτε έναν πάροχο προεπεξεργασίας εγγράφων", "title": "Προεπεξεργασία Εγγράφων"}, "preprocessOrOcr.tooltip": "Ορίστε πάροχο προεπεξεργασίας εγγράφων ή OCR στις Ρυθμίσεις -> Εργαλεία. Η προεπεξεργασία εγγράφων μπορεί να βελτιώσει σημαντικά την απόδοση αναζήτησης για έγγραφα πολύπλοκης μορφής ή εγγράφων σε μορφή σάρωσης. Το OCR μπορεί να αναγνωρίσει μόνο κείμενο μέσα σε εικόνες εγγράφων ή σε PDF σε μορφή σάρωσης.", "title": "Ρυθμίσεις Εργαλείων", "websearch": {"apikey": "Κλειδί API", "blacklist": "Μαύρη Λίστα", "blacklist_description": "Τα αποτελέσματα από τους παρακ<PERSON><PERSON>ω ιστότοπους δεν θα εμφανίζονται στα αποτελέσματα αναζήτησης", "blacklist_tooltip": "Παρακ<PERSON>λώ χρησιμοποιήστε την ακόλουθη μορφή (διαχωρισμός με αλλαγή γραμμής)\nΜοτίβο αντιστοίχισης: *://*.example.com/*\nΚανονική έκφραση: /example\\.(net|org)/", "check": "Έλεγχος", "check_failed": "Αποτυχία επαλήθευσης", "check_success": "Επιτυχής επαλήθευση", "compression": {"cutoff.limit": "<PERSON><PERSON><PERSON><PERSON> αποκοπής", "cutoff.limit.placeholder": "<PERSON><PERSON><PERSON><PERSON> εισαγωγής", "cutoff.limit.tooltip": "Περιορίζει το μήκος του περιεχομένου των αποτελεσμάτων αναζήτησης· το περιεχόμενο που υπερβαίνει το όριο θα αποκόπτεται (π.χ. 2000 χαρακτήρες)", "cutoff.unit.char": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cutoff.unit.token": "Token", "error": {"dimensions_auto_failed": "Αποτυχ<PERSON>α αυτόματης λήψης διαστάσεων", "embedding_model_required": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> επιλέξτε πρώτα ένα μοντέλο ενσωμάτωσης", "provider_not_found": "Ο πάροχος δεν βρέθηκε", "rag_failed": "Το RAG απέτυχε"}, "info": {"dimensions_auto_success": "Η αυτόματη λήψη διαστάσεων ήταν επιτυχής, οι διαστάσεις είναι {{dimensions}}"}, "method": "Μέθο<PERSON>ος συμπίεσης", "method.cutoff": "Αποκοπή", "method.none": "<PERSON><PERSON><PERSON><PERSON><PERSON> συμπίεση", "method.rag": "RAG", "rag.document_count": "Αριθμός αποσπασμάτων εγγράφου", "rag.document_count.tooltip": "Ο αναμενόμενος αριθμός αποσπασμάτων εγγράφου που θα εξαχθούν από κάθε αποτέλεσμα αναζήτησης· ο πραγματικός συνολικός αριθμός είναι αυτή η τιμή επί τον αριθμό των αποτελεσμάτων αναζήτησης", "rag.embedding_dimensions.auto_get": "Αυτόματη λήψη διαστάσεων", "rag.embedding_dimensions.placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> καθορισμό διαστάσεων", "rag.embedding_dimensions.tooltip": "Αν αφεθεί κενό, δεν θα μεταδοθεί η παράμετρος dimensions", "title": "Συμπίεση αποτελεσμάτων αναζήτησης"}, "content_limit": "Όριο μήκους περιεχομένου", "content_limit_tooltip": "Περιορίζει το μήκος του περιεχομένου των αποτελεσμάτων αναζήτησης, το περιεχόμενο πέραν του ορίου θα περικοπεί", "free": "Δωρε<PERSON>ν", "no_provider_selected": "Π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επιλέξτε πάροχο αναζήτησης πριν τον έλεγχο", "overwrite": "Αντικα<PERSON>ά<PERSON>τα<PERSON>η αναζήτησης παρόχου", "overwrite_tooltip": "Εξαναγκάζει τη χρήση του παρόχου αναζήτησης αντί για μοντέλο μεγάλης γλώσσας για αναζήτηση", "search_max_result": "Αριθμ<PERSON>ς αποτελεσμάτων αναζήτησης", "search_max_result.tooltip": "Σε περίπτωση που δεν είναι ενεργοποιημένη η συμπίεση αποτελεσμάτων αναζήτησης, με<PERSON><PERSON><PERSON><PERSON> αριθμός μπορεί να καταναλώσει πολλά tokens", "search_provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αναζήτη<PERSON>ης", "search_provider_placeholder": "Επιλέξτε έναν πάροχο αναζήτησης", "search_with_time": "Αναζήτηση με ημερομηνία", "subscribe": "Εγγραφή σε μαύρη λίστα", "subscribe_add": "Προσθήκη εγγραφής", "subscribe_add_success": "Η πηγή εγγραφής προστέθηκε επιτυχώς!", "subscribe_delete": "Διαγραφή πηγής εγγραφής", "subscribe_name": "Εναλλακτικό όνομα", "subscribe_name.placeholder": "Εναλλακτι<PERSON><PERSON> όνομα που χρησιμοποιείτ<PERSON><PERSON> όταν η ληφθείσα πηγή εγγραφής δεν έχει όνομα", "subscribe_update": "Άμεση ενημέρωση", "subscribe_url": "Διεύθυνση πηγής εγγραφής", "tavily": {"api_key": "Κλειδί Tavily API", "api_key.placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το κλειδ<PERSON> API", "description": "Το <PERSON><PERSON> είναι μια μηχανή αναζήτησης που εξατομικεύεται για AI πράκτορες, παρέχοντας πραγματικού χρόνου, ακ<PERSON><PERSON><PERSON><PERSON> αποτελέσματα, έξυ<PERSON>νες προτάσεις ερωτημάτων και δυνατότητες εμβάθυνσης έρευνας", "title": "<PERSON><PERSON>"}, "title": "Διαδικτυα<PERSON><PERSON> Αναζήτηση"}}, "topic.pin_to_top": "Καρ<PERSON><PERSON>τ<PERSON>ω<PERSON><PERSON> Θέματος στην Κορυφή", "topic.position": "Θέση θεμάτων", "topic.position.left": "Αριστερά", "topic.position.right": "Δεξιά", "topic.show.time": "Εμφάνιση ώρας θέματος", "tray.onclose": "Μειωμένο στη συνδρομή κατά την κλεισιά", "tray.show": "Εμφάνιση εικονιδίου συνδρομής", "tray.title": "Συνδρομή", "zoom": {"reset": "Επαναφορά", "title": "Κλίμακα"}}, "title": {"agents": "Πράκτορες", "apps": "Εφαρμογές", "files": "Αρχεία", "home": "Αρχική Σελίδα", "knowledge": "Βάση Γνώσης", "launchpad": "Πίν<PERSON><PERSON><PERSON><PERSON>ς", "mcp-servers": "Διακ<PERSON><PERSON>ιστές MCP", "memories": "Μνήμες", "paintings": "Ζωγραφική", "settings": "Ρυθμίσεις", "translate": "Μετάφραση"}, "trace": {"backList": "Επιστροφή στη λίστα", "edasSupport": "Λειτουργεί από το Alibaba Cloud EDAS", "endTime": "Ώρα λήξης", "inputs": "Είσοδοι", "label": "Αλυσίδα κλήσης", "name": "Όνομα κόμβου", "noTraceList": "Δεν βρέθηκαν πληροφορίες ίχνους", "outputs": "Έξοδοι", "parentId": "Ανώτερο ID", "spanDetail": "Λεπτομ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n", "spendTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> κατανάλω<PERSON>ης", "startTime": "Ώρα έναρξης", "tag": "Ετικέτα", "tokenUsage": "Χρή<PERSON>η token", "traceWindow": "Παράθυρο αλυσίδας κλήσης"}, "translate": {"alter_language": "Εναλλακτική γλώσσα", "any.language": " οποιαδήπο<PERSON>ε γλώσσα", "button.translate": "Μετάφραση", "close": "Κλείσιμο", "closed": "Η μετάφραση έχει απενεργοποιηθεί", "confirm": {"content": "Μετάφραση θα επικαλύψει το αρχικό κείμενο, συνεχίζει;", "title": "Επιβεβαίωση μετάφρασης"}, "copied": "Το μεταφρασμένο κείμενο αντιγράφηκε", "detected.language": "Αυτόματη ανίχνευση", "empty": "Το μεταφρασμένο κείμενο είναι κενό", "error.failed": "Η μετάφραση απέτυχε", "error.not_configured": "Το μοντέλο μετάφρασης δεν είναι ρυθμισμένο", "history": {"clear": "Καθαρισμ<PERSON>ς ιστορικού", "clear_description": "Η διαγραφή του ιστορικού θα διαγράψει όλα τα απομνημονεύματα μετάφρασης. Θέλετε να συνεχίσετε;", "delete": "Διαγραφή", "empty": "δεν υπάρχουν απομνημονεύματα μετάφρασης", "title": "Ιστορικό μετάφρασης"}, "input.placeholder": "Εισαγάγετε κείμενο για μετάφραση", "language.not_pair": "Η γλώσσα πηγής διαφέρει από την οριζόμενη γλώσσα", "language.same": "Η γλώσσα πηγής και η γλώσσα προορισμού είναι ίδιες", "menu": {"description": "Μετα<PERSON>ράστε το περιεχόμενο του τρέχοντος πεδίου εισαγωγής"}, "not.found": "Δεν βρέθηκε μετάφραση", "output.placeholder": "Μετάφραση", "processing": "Μεταφράζεται...", "settings": {"bidirectional": "Ρύθμιση διπλής κατεύθυνσης μετάφρασης", "bidirectional_tip": "Όταν ενεργοποιηθε<PERSON>, υποστηρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> μόνο διπλής κατεύθυνσης μετάφραση μεταξύ της πηγαίας και της στόχου γλώσσας", "model": "Ρύθμιση μοντέλου", "model_desc": "Μοντ<PERSON><PERSON><PERSON> που χρησιμοποιείται από την υπηρεσία μετάφρασης", "preview": "Προεπισκόπηση Markdown", "scroll_sync": "Ρύθμιση συγχρονισμού κύλισης", "title": "Ρυθμίσεις μετάφρασης"}, "target_language": "Γλώσσα προορισμού", "title": "Μετάφραση", "tooltip.newline": "Αλλαγή γραμμής"}, "tray": {"quit": "Έξοδος", "show_mini_window": "Σύντομη βοήθεια", "show_window": "Εμφάνιση παραθύρου"}, "update": {"install": "Εγκατάσταση", "later": "Μετά", "message": "Νέα έκδοση {{version}} ε<PERSON><PERSON><PERSON><PERSON> έτοιμη, θέλετε να την εγκαταστήσετε τώρα;", "noReleaseNotes": "<PERSON><PERSON><PERSON><PERSON><PERSON> σημειώσεις", "title": "Ενημέρωση"}, "words": {"knowledgeGraph": "γνώσεις Γράφου", "quit": "Έξοδος", "show_window": "Εμφάνιση Παραθύρου", "visualization": "προβολή"}}}