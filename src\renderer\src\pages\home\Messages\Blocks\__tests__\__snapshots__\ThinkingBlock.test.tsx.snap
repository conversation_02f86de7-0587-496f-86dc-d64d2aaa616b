// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ThinkingBlock > basic rendering > should match snapshot 1`] = `
.c0 {
  margin-bottom: 15px;
}

.c0 .ant-collapse-header {
  padding: 0!important;
}

.c0 .ant-collapse-content-box {
  padding: 16px!important;
  border-width: 0 0.5px 0.5px 0.5px;
  border-style: solid;
  border-color: var(--color-border);
  border-radius: 0 0 12px 12px;
}

.c1 {
  position: relative;
}

.c2 {
  background: none;
  border: none;
  color: var(--color-text-2);
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  opacity: 0.6;
  transition: all 0.3s;
  position: absolute;
  right: -12px;
  top: -12px;
}

.c2:hover {
  opacity: 1;
  color: var(--color-text);
}

.c2:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.c2 .iconfont {
  font-size: 14px;
}

<div
  class="c0 message-thought-container"
  data-active-key="thought"
  data-size="small"
  data-testid="collapse-container"
>
  <div
    data-testid="collapse-item-thought"
  >
    <div
      data-testid="collapse-header-thought"
    >
      <div
        data-content="I need to think about this carefully..."
        data-expanded="true"
        data-is-thinking="false"
        data-testid="mock-marquee-component"
      >
        <div
          data-testid="thinking-time-text"
        >
          Thought for 5.0s
        </div>
      </div>
    </div>
    <div
      data-testid="collapse-content-thought"
    >
      <div
        class="c1"
        style="font-family: var(--font-family); font-size: 14px;"
      >
        <div
          data-mouse-enter-delay="0.8"
          data-testid="tooltip"
          title="Copy"
        >
          <button
            aria-label="Copy"
            class="c2 message-action-button"
          >
            <i
              class="iconfont icon-copy"
            />
          </button>
        </div>
        <div
          data-block-id="test-thinking-block-1"
          data-testid="mock-markdown"
        >
          Markdown: 
          I need to think about this carefully...
        </div>
      </div>
    </div>
  </div>
</div>
`;
