// src/renderer/src/services/Live2DService.ts
// 全局 Live2D 服务，可被任何窗口使用

import { loggerService } from '@logger'
import { EventEmitter, EVENT_NAMES } from './EventService'
import { store } from '@renderer/store'
import { 
  setLoading, 
  setError, 
  setReady, 
  setCurrentAnimation,
  setExpanded,
  setFileUrl
} from '@renderer/store/virtualCharacter'

const logger = loggerService.withContext('Live2DService')

export interface Live2DAnimation {
  name: string
  loop?: boolean
  priority?: number
}

export interface Live2DModel {
  id: string
  name: string
  path: string
  animations: string[]
}

class Live2DService {
  private static instance: Live2DService
  private initialized = false
  private activeWindows = new Set<string>() // 跟踪哪些窗口在使用 Live2D
  private animationQueue: Live2DAnimation[] = []
  private currentFileUrl: string | null = null

  private constructor() {
    this.setupEventListeners()
  }

  static getInstance(): Live2DService {
    if (!Live2DService.instance) {
      Live2DService.instance = new Live2DService()
    }
    return Live2DService.instance
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      logger.debug('Live2D 服务已初始化')
      return
    }

    try {
      logger.info('初始化 Live2D 服务')
      store.dispatch(setLoading(true))

      // 获取应用信息和文件路径
      await this.initializeFileUrl()
      
      this.initialized = true
      store.dispatch(setReady(true))
      store.dispatch(setLoading(false))
      
      logger.info('Live2D 服务初始化成功')
      EventEmitter.emit(EVENT_NAMES.LIVE2D_READY)
      
    } catch (error) {
      logger.error('Live2D 服务初始化失败', error as Error)
      store.dispatch(setError((error as Error).message))
      store.dispatch(setLoading(false))
      EventEmitter.emit(EVENT_NAMES.LIVE2D_ERROR, error)
      throw error
    }
  }

  /**
   * 初始化文件URL
   */
  private async initializeFileUrl(): Promise<void> {
    try {
      const appInfo = await window.api.getAppInfo()
      const fileUrl = `file://${appInfo.appPath}/src/renderer/src/windows/mini/virtualCharacter/live2d/live2d.html?appPath=${encodeURIComponent(appInfo.appPath)}`
      
      this.currentFileUrl = fileUrl
      store.dispatch(setFileUrl(fileUrl))
      
      logger.info('Live2D 文件URL初始化成功', { fileUrl })
    } catch (error) {
      logger.error('Live2D 文件URL初始化失败', error as Error)
      throw new Error('无法获取 Live2D 文件路径')
    }
  }

  /**
   * 注册窗口使用 Live2D
   */
  registerWindow(windowId: string): void {
    this.activeWindows.add(windowId)
    logger.debug('注册 Live2D 窗口', { windowId, totalWindows: this.activeWindows.size })
  }

  /**
   * 注销窗口使用 Live2D
   */
  unregisterWindow(windowId: string): void {
    this.activeWindows.delete(windowId)
    logger.debug('注销 Live2D 窗口', { windowId, totalWindows: this.activeWindows.size })
  }

  /**
   * 播放动画
   */
  async playAnimation(animation: Live2DAnimation): Promise<void> {
    if (!this.initialized) {
      logger.warn('Live2D 服务未初始化，无法播放动画')
      return
    }

    try {
      logger.info('播放 Live2D 动画', animation)
      
      // 更新 Redux 状态
      store.dispatch(setCurrentAnimation(animation.name))
      
      // 发送事件给所有监听的组件
      EventEmitter.emit(EVENT_NAMES.LIVE2D_PLAY_ANIMATION, animation)
      
      // 如果有优先级，插入到队列前面
      if (animation.priority && animation.priority > 0) {
        this.animationQueue.unshift(animation)
      } else {
        this.animationQueue.push(animation)
      }
      
    } catch (error) {
      logger.error('播放动画失败', error as Error)
      EventEmitter.emit(EVENT_NAMES.LIVE2D_ERROR, error)
      throw error
    }
  }

  /**
   * 设置表情
   */
  async setExpression(expression: string): Promise<void> {
    if (!this.initialized) {
      logger.warn('Live2D 服务未初始化，无法设置表情')
      return
    }

    try {
      logger.info('设置 Live2D 表情', { expression })
      EventEmitter.emit(EVENT_NAMES.LIVE2D_SET_EXPRESSION, { expression })
    } catch (error) {
      logger.error('设置表情失败', error as Error)
      EventEmitter.emit(EVENT_NAMES.LIVE2D_ERROR, error)
      throw error
    }
  }

  /**
   * 设置姿势
   */
  async setPose(pose: string): Promise<void> {
    if (!this.initialized) {
      logger.warn('Live2D 服务未初始化，无法设置姿势')
      return
    }

    try {
      logger.info('设置 Live2D 姿势', { pose })
      EventEmitter.emit(EVENT_NAMES.LIVE2D_SET_POSE, { pose })
    } catch (error) {
      logger.error('设置姿势失败', error as Error)
      EventEmitter.emit(EVENT_NAMES.LIVE2D_ERROR, error)
      throw error
    }
  }

  /**
   * 发送消息给 Live2D
   */
  async sendMessage(message: string): Promise<void> {
    if (!this.initialized) {
      logger.warn('Live2D 服务未初始化，无法发送消息')
      return
    }

    try {
      logger.info('发送消息给 Live2D', { message })
      EventEmitter.emit(EVENT_NAMES.LIVE2D_SEND_MESSAGE, { message })
    } catch (error) {
      logger.error('发送消息失败', error as Error)
      EventEmitter.emit(EVENT_NAMES.LIVE2D_ERROR, error)
      throw error
    }
  }

  /**
   * 设置展开模式
   */
  setExpandedMode(expanded: boolean): void {
    logger.info('设置 Live2D 展开模式', { expanded })
    store.dispatch(setExpanded(expanded))
  }

  /**
   * 获取当前文件URL
   */
  getFileUrl(): string | null {
    return this.currentFileUrl
  }

  /**
   * 获取活跃窗口数量
   */
  getActiveWindowCount(): number {
    return this.activeWindows.size
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听 AI 回复事件
    EventEmitter.on(EVENT_NAMES.AI_RESPONSE_RECEIVED, this.handleAIResponse.bind(this))
    
    logger.debug('Live2D 服务事件监听器设置完成')
  }

  /**
   * 处理 AI 回复
   */
  private handleAIResponse(data: { content: string }): void {
    logger.debug('收到 AI 回复，准备播放相应动画', { 
      contentLength: data.content.length,
      isInitialized: this.initialized 
    })

    if (!this.initialized) {
      logger.warn('Live2D 服务未初始化，跳过 AI 回复处理')
      return
    }

    // 根据 AI 回复内容选择合适的动画
    const animations = this.parseAnimationsFromContent(data.content)
    
    if (animations.length > 0) {
      // 播放第一个匹配的动画
      this.playAnimation(animations[0]).catch(error => {
        logger.error('播放 AI 回复动画失败', error)
      })
    } else {
      // 播放默认的说话动画
      this.playAnimation({ name: 'Idle', loop: false }).catch(error => {
        logger.error('播放默认动画失败', error)
      })
    }
  }

  /**
   * 从内容中解析动画命令
   */
  private parseAnimationsFromContent(content: string): Live2DAnimation[] {
    const animations: Live2DAnimation[] = []
    
    // 简单的关键词匹配
    const animationMap: Record<string, string> = {
      '开心': 'Happy',
      '高兴': 'Happy', 
      '快乐': 'Happy',
      '惊讶': 'Surprised',
      '思考': 'Thinking',
      '困惑': 'Confused',
      '兴奋': 'Excited',
      '打招呼': 'Greeting',
      '你好': 'Greeting'
    }

    for (const [keyword, animationName] of Object.entries(animationMap)) {
      if (content.includes(keyword)) {
        animations.push({ name: animationName, loop: false })
        break // 只取第一个匹配的
      }
    }

    return animations
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    logger.info('销毁 Live2D 服务')
    
    // 移除事件监听器
    EventEmitter.off(EVENT_NAMES.AI_RESPONSE_RECEIVED, this.handleAIResponse.bind(this))
    
    // 清理状态
    this.activeWindows.clear()
    this.animationQueue = []
    this.initialized = false
    this.currentFileUrl = null
    
    // 重置 Redux 状态
    store.dispatch(setReady(false))
    store.dispatch(setLoading(false))
    store.dispatch(setError(null))
  }
}

// 导出单例实例
export const live2DService = Live2DService.getInstance()
export default live2DService
