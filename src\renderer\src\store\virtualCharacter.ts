import { createSlice, PayloadAction } from '@reduxjs/toolkit'

/**
 * Live2D虚拟角色状态管理
 * 简化版本，集成到Redux store中
 */

// Live2D 模型接口
interface Live2DModel {
  id: string
  name: string
  path: string
  animations: string[]
}

export interface VirtualCharacterState {
  // 基础状态
  isExpanded: boolean
  isLoading: boolean
  error: string | null
  fileUrl: string

  // Live2D状态
  isVisible: boolean  // 新增：是否显示
  isReady: boolean
  currentModel: Live2DModel | null  // 新增：当前模型
  currentAnimation: string | null
  availableAnimations: string[]  // 新增：可用动画列表

  // AI回复监听
  isListening: boolean
  queueCount: number
}

const initialState: VirtualCharacterState = {
  isExpanded: false,
  isLoading: true,
  error: null,
  fileUrl: '',
  isVisible: false,  // 新增：默认隐藏
  isReady: false,
  currentModel: null,  // 新增：无模型
  currentAnimation: null,
  availableAnimations: [],  // 新增：空动画列表
  isListening: false,
  queueCount: 0
}

const virtualCharacterSlice = createSlice({
  name: 'virtualCharacter',
  initialState,
  reducers: {
    // 基础状态管理
    setExpanded: (state, action: PayloadAction<boolean>) => {
      state.isExpanded = action.payload
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
    setFileUrl: (state, action: PayloadAction<string>) => {
      state.fileUrl = action.payload
    },

    // Live2D状态管理
    setLive2DVisible: (state, action: PayloadAction<boolean>) => {
      state.isVisible = action.payload
    },
    setCurrentModel: (state, action: PayloadAction<Live2DModel | null>) => {
      state.currentModel = action.payload
      if (action.payload) {
        state.availableAnimations = action.payload.animations
      } else {
        state.availableAnimations = []
      }
    },
    setReady: (state, action: PayloadAction<boolean>) => {
      state.isReady = action.payload
      if (action.payload) {
        state.isLoading = false
        state.error = null
      }
    },
    setCurrentAnimation: (state, action: PayloadAction<string | null>) => {
      state.currentAnimation = action.payload
    },

    // AI监听状态
    setListening: (state, action: PayloadAction<boolean>) => {
      state.isListening = action.payload
    },
    setQueueCount: (state, action: PayloadAction<number>) => {
      state.queueCount = action.payload
    },

    // 重置状态
    resetVirtualCharacter: () => initialState,

    // 批量更新状态
    updateVirtualCharacterState: (state, action: PayloadAction<Partial<VirtualCharacterState>>) => {
      return { ...state, ...action.payload }
    }
  }
})

export const {
  setExpanded,
  setLoading,
  setError,
  setFileUrl,
  setLive2DVisible,
  setCurrentModel,
  setReady,
  setCurrentAnimation,
  setListening,
  setQueueCount,
  resetVirtualCharacter,
  updateVirtualCharacterState
} = virtualCharacterSlice.actions

// 选择器
export const selectVirtualCharacter = (state: { virtualCharacter: VirtualCharacterState }) => state.virtualCharacter
export const selectIsExpanded = (state: { virtualCharacter: VirtualCharacterState }) =>
  state.virtualCharacter.isExpanded
export const selectIsLoading = (state: { virtualCharacter: VirtualCharacterState }) => state.virtualCharacter.isLoading
export const selectError = (state: { virtualCharacter: VirtualCharacterState }) => state.virtualCharacter.error
export const selectIsReady = (state: { virtualCharacter: VirtualCharacterState }) => state.virtualCharacter.isReady

export default virtualCharacterSlice.reducer
