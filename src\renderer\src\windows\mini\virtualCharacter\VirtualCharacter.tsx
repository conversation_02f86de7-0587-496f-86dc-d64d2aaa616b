import { CompressOutlined, ExpandOutlined } from '@ant-design/icons'
import { loggerService } from '@logger'
import { useAppDispatch, useAppSelector } from '@renderer/store'
import { selectIsExpanded, setExpanded } from '@renderer/store/virtualCharacter'
import { Button } from 'antd'
import { FC, useEffect } from 'react'
import styled from 'styled-components'

import Live2DViewer from './components/Live2DViewer'
import { useLive2D } from './hooks/useLive2D'
import { VirtualCharacterProps } from './types'

const logger = loggerService.withContext('VirtualCharacter')

/**
 * Live2D虚拟角色主组件
 * 整合了所有Live2D相关功能
 */
const VirtualCharacter: FC<VirtualCharacterProps> = ({ isExpanded: propIsExpanded, onToggleExpand }) => {
  const dispatch = useAppDispatch()
  const isExpanded = useAppSelector(selectIsExpanded)

  // 使用统一的Live2D hook
  const { isLoading, error, fileUrl, isReady, webviewRef, setExpandedMode, retryLoad } = useLive2D()

  // 同步外部传入的展开状态
  useEffect(() => {
    if (propIsExpanded !== undefined && propIsExpanded !== isExpanded) {
      dispatch(setExpanded(propIsExpanded))
    }
  }, [propIsExpanded, isExpanded, dispatch])

  // 同步展开状态到Live2D
  useEffect(() => {
    if (isReady) {
      setExpandedMode(isExpanded)
    }
  }, [isExpanded, isReady, setExpandedMode])

  // 处理展开/收起切换
  const handleToggleExpand = () => {
    const newExpanded = !isExpanded
    dispatch(setExpanded(newExpanded))
    onToggleExpand?.(newExpanded)

    logger.info('Live2D虚拟角色展开状态切换', {
      from: isExpanded,
      to: newExpanded
    })
  }

  // 处理错误重试
  const handleRetry = () => {
    logger.info('Live2D虚拟角色重试加载')
    retryLoad()
  }

  // 处理错误忽略
  const handleDismissError = () => {
    logger.info('Live2D虚拟角色忽略错误')
    // 可以添加忽略错误的逻辑
  }

  logger.debug('VirtualCharacter渲染', {
    isExpanded,
    isLoading,
    hasError: !!error,
    isReady,
    hasFileUrl: !!fileUrl
  })

  return (
    <Container $isExpanded={isExpanded}>
      {/* 控制按钮 */}
      <ExpandButton
        type="text"
        icon={isExpanded ? <CompressOutlined /> : <ExpandOutlined />}
        onClick={handleToggleExpand}
        title={isExpanded ? '收起虚拟角色' : '展开虚拟角色'}
        size="small"
      />

      {/* 加载状态 */}
      {isLoading && (
        <LoadingOverlay>
          <LoadingSpinner />
        </LoadingOverlay>
      )}

      {/* 错误状态 */}
      {error && (
        <ErrorOverlay>
          <ErrorContent>
            <ErrorIcon>❌</ErrorIcon>
            <ErrorTitle>加载失败</ErrorTitle>
            <ErrorMessage>{typeof error === 'string' ? error : error}</ErrorMessage>
            <ErrorActions>
              <Button type="primary" size="small" onClick={handleRetry}>
                重试
              </Button>
              <Button size="small" onClick={handleDismissError}>
                关闭
              </Button>
            </ErrorActions>
          </ErrorContent>
        </ErrorOverlay>
      )}

      {/* Live2D显示器 */}
      <Live2DViewer ref={webviewRef} fileUrl={fileUrl} isLoading={isLoading} hasError={!!error} />
    </Container>
  )
}

// 样式组件
const Container = styled.div<{ $isExpanded: boolean }>`
  position: relative;
  width: ${(props) => (props.$isExpanded ? '100%' : '200px')};
  height: 100%;
  background: transparent;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-app-region: no-drag;

  /* 展开时的样式 */
  ${(props) =>
    props.$isExpanded &&
    `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    width: 100% !important;
  `}
`

const ExpandButton = styled(Button)`
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  border-radius: 8px !important;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-app-region: no-drag;
  opacity: 0.8;

  &:hover {
    background: rgba(0, 0, 0, 0.2) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 1;
  }

  &:active {
    background: rgba(0, 0, 0, 0.15) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    opacity: 0.9;
  }

  &:focus {
    outline: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }
`

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background);
  z-index: 10;
  -webkit-app-region: no-drag;
`

const LoadingSpinner = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-border);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`

const ErrorOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background);
  z-index: 10;
  padding: 20px;
  -webkit-app-region: no-drag;
`

const ErrorContent = styled.div`
  text-align: center;
  max-width: 80%;
`

const ErrorIcon = styled.div`
  font-size: 24px;
  margin-bottom: 8px;
`

const ErrorTitle = styled.h4`
  color: var(--color-text);
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
`

const ErrorMessage = styled.div`
  color: var(--color-error);
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 12px;
  word-break: break-word;
`

const ErrorActions = styled.div`
  display: flex;
  justify-content: center;
  gap: 8px;
`

export default VirtualCharacter
