/**
 * 简化的Live2D通信桥接脚本
 * 只保留核心通信功能
 */

// 消息队列，用于缓存未就绪时的消息

let messageQueue = []
let isReady = false

// 发送消息到Live2D页面
function sendToLive2D(action, data = {}) {
  const message = {
    source: 'live2d-parent',
    action,
    ...data,
    timestamp: Date.now()
  }

  if (isReady) {
    window.postMessage(message, '*')
  } else {
    messageQueue.push(message)
  }
}

// 处理队列中的消息
function processMessageQueue() {
  while (messageQueue.length > 0) {
    const message = messageQueue.shift()
    window.postMessage(message, '*')
  }
}

// 监听来自Live2D页面的消息
window.addEventListener('message', (event) => {
  if (event.data && event.data.source === 'live2d') {
    const { type, action, data } = event.data

    switch (action) {
      case 'ready':
        isReady = true
        processMessageQueue()
        console.log('Live2D准备就绪')
        break

      case 'error':
        console.error('Live2D错误:', data)
        break

      default:
        console.log('Live2D消息:', event.data)
    }
  }
})

// 导出API
window.Live2DAPI = {
  // 播放动画
  playMotion: (motionName) => {
    sendToLive2D('playMotion', { motionName })
  },

  // 设置展开模式
  setExpanded: (expanded) => {
    sendToLive2D('setExpanded', { expanded })
  },

  // 发送消息
  sendMessage: (message) => {
    sendToLive2D('sendMessage', { message })
  },

  // 检查是否就绪
  isReady: () => isReady
}
