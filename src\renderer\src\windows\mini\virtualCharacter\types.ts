import { WebviewTag } from 'electron'

/**
 * Live2D虚拟角色类型定义
 */

// 主组件Props
export interface VirtualCharacterProps {
  isExpanded?: boolean
  onToggleExpand?: (expanded: boolean) => void
}

// Live2D消息类型
export interface Live2DMessage {
  type: 'command' | 'event' | 'response'
  action?: string
  data?: any
  timestamp?: number
}

// 动画类型
export type AnimationType = 'action' | 'expression' | 'pose'

// 动画指令
export interface AnimationCommand {
  type: AnimationType
  name: string
  priority?: number
}

// Webview引用类型
export type WebviewRef = React.RefObject<WebviewTag | null>

// 应用信息类型
export interface AppInfo {
  appPath: string
  version: string
  isPackaged: boolean
}

// 扩展Window接口
declare global {
  interface Window {
    api: {
      getAppInfo: () => Promise<AppInfo>
      miniWindow: {
        setPin: (pinned: boolean) => void
        hide: () => void
      }
    }
  }
}

// 常量
export const LIVE2D_CONSTANTS = {
  FILE_PATH: '/src/renderer/src/windows/mini/virtualCharacter/live2d/live2d.html',
  WEBVIEW_PARTITION: 'persist:webview',
  ANIMATION_DURATION: 300,
  WEBVIEW_READY_DELAY: 500
} as const
