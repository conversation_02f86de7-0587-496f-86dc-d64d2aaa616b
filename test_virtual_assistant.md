# 虚拟助手测试指南

## 测试步骤

### 1. 启动Cherry Studio开发环境

```bash
# 安装依赖（如果还没有安装）
yarn install

# 启动开发服务器
yarn dev
```

### 2. 测试虚拟助手功能

1. **打开迷你窗口**

   - 启动Cherry Studio后，使用快捷键或菜单打开迷你窗口
   - 或者在设置中启用快速助手功能

2. **访问虚拟助手**

   - 在迷你窗口的功能菜单中，应该能看到"🌸 虚拟助手"选项
   - 点击该选项进入虚拟助手界面

3. **验证Live2D加载**

   - 检查是否显示"正在加载模型..."提示
   - 等待Live2D模型加载完成
   - 如果加载失败，检查控制台错误信息

4. **测试交互功能**

   - 点击Live2D模型，观察是否有动作反应
   - 移动鼠标，观察模型视线是否跟随
   - 检查状态栏是否显示交互反馈

5. **测试导航功能**
   - 点击"返回"按钮，确认能正常返回主界面
   - 重新进入虚拟助手，验证状态保持

## 可能遇到的问题

### 1. 模型加载失败

**症状**: 显示"模型加载失败"错误

**解决方案**:

- 检查`my_experiment/hiyori_pro_zh/`目录是否存在
- 确认模型文件`runtime/hiyori_pro_t11.model3.json`存在
- 检查网络连接，确保能访问CDN资源

### 2. webview不显示

**症状**: 虚拟助手页面空白或显示错误

**解决方案**:

- 检查Electron的webview功能是否启用
- 查看开发者工具的控制台错误
- 确认webview的src路径正确

### 3. 交互无响应

**症状**: 点击模型没有反应

**解决方案**:

- 检查Live2D库是否正确加载
- 确认模型文件完整性
- 查看浏览器控制台的JavaScript错误

### 4. 菜单项不显示

**症状**: 在功能菜单中看不到虚拟助手选项

**解决方案**:

- 确认`FeatureMenus.tsx`的修改已生效
- 重新编译并重启应用
- 检查TypeScript编译错误

## 调试技巧

### 1. 开启开发者工具

在虚拟助手页面右键选择"检查元素"，或者在webview上使用快捷键打开开发者工具。

### 2. 查看控制台日志

Live2D页面会输出详细的日志信息，包括：

- 模型加载状态
- 交互事件
- 错误信息

### 3. 网络面板

检查网络面板确认所有资源都正确加载：

- Live2D核心库
- PIXI.js库
- 模型文件和纹理

### 4. 性能监控

使用性能面板监控：

- 内存使用情况
- CPU占用率
- 渲染性能

## 成功标志

如果看到以下现象，说明集成成功：

1. ✅ 迷你窗口功能菜单中显示"🌸 虚拟助手"
2. ✅ 点击后能正常跳转到虚拟助手界面
3. ✅ Live2D模型正确加载并显示
4. ✅ 点击模型有动作反应
5. ✅ 鼠标移动时模型视线跟随
6. ✅ 状态栏显示交互反馈信息
7. ✅ 返回按钮功能正常

## 下一步测试

成功完成基础测试后，可以进行以下高级测试：

### 1. 消息通信测试

在开发者工具控制台中执行：

```javascript
window.receiveMessage('测试消息')
```

应该看到虚拟助手显示消息并播放动作。

### 2. 动作播放测试

```javascript
window.playMotion('Idle')
```

应该看到模型播放指定动作。

### 3. 表情设置测试

```javascript
window.setExpression('happy')
```

应该看到状态栏显示表情设置信息。

## 报告问题

如果遇到问题，请提供以下信息：

1. 操作系统和版本
2. Node.js版本
3. 错误截图
4. 控制台错误日志
5. 网络面板信息
6. 具体的复现步骤

这将帮助快速定位和解决问题。
