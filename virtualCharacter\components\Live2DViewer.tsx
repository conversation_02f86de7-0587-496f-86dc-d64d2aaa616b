import { loggerService } from '@logger'
import { WebviewTag } from 'electron'
import styled from 'styled-components'

import { LIVE2D_CONSTANTS } from '../types'

const logger = loggerService.withContext('Live2DViewer')

interface Live2DViewerProps {
  fileUrl: string
  isLoading: boolean
  hasError: boolean
}

/**
 * 简化的Live2D显示组件
 */
const Live2DViewer = ({
  ref,
  fileUrl,
  isLoading,
  hasError
}: Live2DViewerProps & { ref?: React.RefObject<WebviewTag | null> }) => {
  logger.debug('Live2DViewer rendering', {
    hasFileUrl: !!fileUrl,
    isLoading,
    hasError
  })

  return (
    <StyledWebview
      style={{
        opacity: isLoading || hasError ? 0 : 1,
        pointerEvents: isLoading || hasError ? 'none' : 'auto'
      }}>
      <webview
        ref={ref}
        src={fileUrl || 'about:blank'}
        partition={LIVE2D_CONSTANTS.WEBVIEW_PARTITION}
        style={{ width: '100%', height: '100%' }}
      />
    </StyledWebview>
  )
}

Live2DViewer.displayName = 'Live2DViewer'

const StyledWebview = styled.div`
  width: 100%;
  height: 100%;
  -webkit-app-region: no-drag;

  webview {
    width: 100%;
    height: 100%;
    border: none;
    transition: opacity 0.3s ease;
    -webkit-app-region: no-drag;
  }
`

export default Live2DViewer
