import { loggerService } from '@logger'
import { EVENT_NAMES, EventEmitter } from '@renderer/services/EventService'
import { useAppDispatch, useAppSelector } from '@renderer/store'
import {
  selectVirtualCharacter,
  setError,
  setFileUrl,
  setListening,
  setLoading,
  setQueueCount,
  setReady
} from '@renderer/store/virtualCharacter'
import { WebviewTag } from 'electron'
import { useCallback, useEffect, useRef } from 'react'

import { AnimationCommand, AppInfo, LIVE2D_CONSTANTS, WebviewRef } from '../types'

const logger = loggerService.withContext('useLive2D')

/**
 * 统一的Live2D Hook
 * 整合所有Live2D相关的状态管理和交互逻辑
 */
export const useLive2D = () => {
  const dispatch = useAppDispatch()
  const virtualCharacterState = useAppSelector(selectVirtualCharacter)
  const webviewRef = useRef<WebviewTag>(null)
  const animationQueueRef = useRef<AnimationCommand[]>([])

  // 初始化文件URL
  useEffect(() => {
    const initFileUrl = async () => {
      try {
        const appInfo: AppInfo = await window.api.getAppInfo()
        const fileUrl = `file://${appInfo.appPath}${LIVE2D_CONSTANTS.FILE_PATH}?appPath=${encodeURIComponent(appInfo.appPath)}`
        dispatch(setFileUrl(fileUrl))
        logger.info('Live2D文件URL初始化成功', { fileUrl })
      } catch (error) {
        logger.error('Live2D文件URL初始化失败', error as Error)
        dispatch(setError('无法获取文件路径'))
      }
    }
    initFileUrl()
  }, [dispatch])

  // 设置webview事件监听
  useEffect(() => {
    const webview = webviewRef.current
    if (!webview) return

    const handleLoadStart = () => {
      logger.debug('Live2D webview开始加载')
      dispatch(setLoading(true))
      dispatch(setError(null))
    }

    const handleLoadStop = () => {
      logger.debug('Live2D webview加载完成')
      dispatch(setLoading(false))

      // 延迟设置通信，等待Live2D初始化
      setTimeout(() => {
        setupCommunication()
      }, LIVE2D_CONSTANTS.WEBVIEW_READY_DELAY)
    }

    const handleLoadError = (event: Electron.DidFailLoadEvent) => {
      logger.error('Live2D webview加载失败', event)
      dispatch(setLoading(false))
      dispatch(setError('Live2D加载失败'))
    }

    webview.addEventListener('dom-ready', handleLoadStart)
    webview.addEventListener('did-finish-load', handleLoadStop)
    webview.addEventListener('did-fail-load', handleLoadError)

    return () => {
      webview.removeEventListener('dom-ready', handleLoadStart)
      webview.removeEventListener('did-finish-load', handleLoadStop)
      webview.removeEventListener('did-fail-load', handleLoadError)
    }
  }, [dispatch])

  // 设置通信
  const setupCommunication = useCallback(() => {
    try {
      // 简化的通信设置，直接标记为就绪
      dispatch(setReady(true))
      logger.info('Live2D通信设置完成')

      // 处理队列中的动画
      processAnimationQueue()
    } catch (error) {
      logger.error('Live2D通信设置失败', error)
      dispatch(setError('通信设置失败'))
    }
  }, [dispatch])

  // 处理动画队列
  const processAnimationQueue = useCallback(() => {
    if (!virtualCharacterState.isReady || animationQueueRef.current.length === 0) return

    const command = animationQueueRef.current.shift()
    if (command) {
      // 这里暂时不调用playAnimation，避免循环依赖
      dispatch(setQueueCount(animationQueueRef.current.length))
    }
  }, [virtualCharacterState.isReady, dispatch])

  // 发送PostMessage到Live2D页面
  const sendToLive2D = useCallback(
    (action: string, data: any = {}) => {
      const webview = webviewRef.current
      if (!webview) {
        logger.warn('Webview引用不存在')
        return
      }

      // 检查webview是否已经附加到DOM并且dom-ready事件已触发
      if (!virtualCharacterState.isReady) {
        // 添加到队列
        if (action === 'playMotion') {
          animationQueueRef.current.push({ type: 'action', name: data.motionName || 'Idle' })
          dispatch(setQueueCount(animationQueueRef.current.length))
        }
        return
      }

      try {
        webview.executeJavaScript(`
        window.postMessage({
          source: 'live2d-parent',
          action: '${action}',
          ...${JSON.stringify(data)},
          timestamp: Date.now()
        }, '*');
      `)
        logger.debug('发送Live2D消息', { action, data })
      } catch (error) {
        logger.error('发送Live2D消息失败', { action, data, error })
      }
    },
    [virtualCharacterState.isReady, dispatch]
  )

  // 播放动画
  const playAnimation = useCallback(
    (animationName: string) => {
      sendToLive2D('playMotion', { motionName: animationName })
    },
    [sendToLive2D]
  )

  // 发送消息
  const sendMessage = useCallback(
    (message: string) => {
      sendToLive2D('sendMessage', { message })
    },
    [sendToLive2D]
  )

  // 设置展开模式
  const setExpandedMode = useCallback(
    (expanded: boolean) => {
      sendToLive2D('setExpanded', { expanded })
    },
    [sendToLive2D]
  )

  // 监听AI回复事件
  useEffect(() => {
    const handleAIResponse = (data: { content: string }) => {
      logger.debug('AI_RESPONSE_RECEIVED event received', {
        contentLength: data.content.length,
        isReady: virtualCharacterState.isReady
      })

      if (!virtualCharacterState.isReady) {
        logger.debug('Live2D not ready, skipping animation processing')
        return
      }

      // 简单的动画指令解析
      const animationMatch = data.content.match(/\[action:(\w+)\]/g)
      if (animationMatch) {
        logger.debug('Found animation commands', { commands: animationMatch })
        animationMatch.forEach((match) => {
          const animationName = match.replace(/\[action:|\]/g, '')
          logger.debug('Playing animation', { animationName })
          playAnimation(animationName)
        })
      } else {
        logger.debug('No animation commands found in content')
      }
    }

    logger.debug('Registering AI_RESPONSE_RECEIVED event listener')
    EventEmitter.on(EVENT_NAMES.AI_RESPONSE_RECEIVED, handleAIResponse)
    dispatch(setListening(true))

    return () => {
      logger.debug('Unregistering AI_RESPONSE_RECEIVED event listener')
      EventEmitter.off(EVENT_NAMES.AI_RESPONSE_RECEIVED, handleAIResponse)
      dispatch(setListening(false))
    }
  }, [playAnimation, dispatch]) // 移除 virtualCharacterState.isReady 依赖，在回调内部检查

  // 重试加载
  const retryLoad = useCallback(() => {
    logger.info('重试加载Live2D')
    dispatch(setLoading(true))
    dispatch(setError(null))
    dispatch(setReady(false))

    // 重新加载webview
    const webview = webviewRef.current
    if (webview && virtualCharacterState.fileUrl) {
      webview.src = virtualCharacterState.fileUrl
    }
  }, [dispatch, virtualCharacterState.fileUrl])

  return {
    // 状态
    ...virtualCharacterState,

    // Webview引用
    webviewRef: webviewRef as WebviewRef,

    // 方法
    playAnimation,
    sendMessage,
    setExpandedMode,
    retryLoad
  }
}
